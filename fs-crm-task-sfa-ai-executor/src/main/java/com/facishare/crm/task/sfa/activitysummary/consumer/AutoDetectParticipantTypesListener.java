package com.facishare.crm.task.sfa.activitysummary.consumer;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.activity.mongo.ActivityMongoDao;
import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.activitysummary.model.ActivityTagMessage;
import com.facishare.crm.task.sfa.activitysummary.model.DetectParticipantTypesModel.UserType;
import com.facishare.crm.task.sfa.activitysummary.service.CompletionsService;
import com.facishare.crm.task.sfa.activitysummary.service.FixJSONFormatService;
import com.facishare.crm.task.sfa.activitysummary.service.paragraph.ParagraphProducer;
import com.facishare.crm.task.sfa.common.util.Safes;
import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import com.facishare.crm.task.sfa.util.OriginCorpusUtils;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.functions.utils.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AutoDetectParticipantTypesListener extends AbstractActivityCommonListener  {

    @Autowired
    private ServiceFacade serviceFacade;

    @Autowired
    private CompletionsService completionsService;

    @Autowired
    private ActivityMongoDao activityMongoDao;

    @Autowired
    private FixJSONFormatService fixJSONFormatService;

    @Autowired
    private ParagraphProducer paragraphProducer;


    private static final List<String> SUPPORT_STAGE_LIST = Lists.newArrayList("file2text", "realtime2textDone");

    private static final String prompt =  "prompt_sfa_activity_auto_detect_participant_types";


    @Override
    String getSection() {
        return "activity-auto-detect-participant-types-consumer";
    }


    @Override
    void consume(ActivityMessage activityMessage) {
        log.info("AutoDetectParticipantTypesListener consume activityMessage:{}", activityMessage);

        String tenantId = activityMessage.getTenantId();
        if (unsupportedMessage(activityMessage)){
            return;
        }

        List<IObjectData> activityUserList = queryActivityUserList(activityMessage, tenantId);
        if (Safes.isEmpty(activityUserList)) {
            log.warn("No activity user found for active record: {}", activityMessage.getObjectId());
            return;
        }

        List<InteractiveDocument> documents = activityMongoDao.queryListByActiveRecordId(tenantId, activityMessage.getObjectId(), 0, 500, true);
        String corpusOriginText = OriginCorpusUtils.makeUpCorpusOriginText(documents, activityUserList);
        String userNames = activityUserList.stream().map(d -> d.get("user_name", String.class)).collect(Collectors.joining("、"));

        Map<String, Object> sceneVariables = Maps.newHashMap();
        sceneVariables.put("userNames", userNames);
        sceneVariables.put("corpus", corpusOriginText);

        AiRestProxyModel.Arg arg = AiRestProxyModel.Arg.builder()
                .apiName(prompt)
                .bingObjectDataId(activityMessage.getObjectId())
                .sceneVariables(sceneVariables)
                .build();

        String completionRst = completionsService.requestCompletion(User.systemUser(tenantId), arg);
        completionRst = fixJSONFormatService.fixJSON(completionRst, true);
        List<UserType> userTypeList = fixJSONFormatService.getDataListFixedInvalidJSON(User.systemUser(tenantId), "", completionRst, UserType.class, true);

        if (Safes.isEmpty(userTypeList)) {
            log.warn("No user types detected for active record, completionRst: {}", completionRst);
            return;
        }

        Map<String, String> userTypeMap = userTypeList.stream().collect(Collectors.toMap(UserType::getUserName, UserType::getParticipantType, (a, b) -> a));
        List<IObjectData> updateList = Lists.newArrayList();
        for (IObjectData activityUser : activityUserList) {
            String userName = activityUser.get("user_name", String.class);
            String types = activityUser.get("participant_types", String.class);
            if (Safes.isNotEmpty(types)) {
                log.warn("Participant types already set for user: {}, activeRecordId: {}, types: {}", userName, activityMessage.getObjectId(), types);
                continue;
            }
            String participantType = userTypeMap.get(userName);
            if (Safes.isEmpty(participantType)) {
                log.warn("No participant type found for user: {}, activeRecordId: {}", userName, activityMessage.getObjectId());
                continue;
            }
            if (!StringUtils.equalsAny(participantType, "our_side", "their_side")) {
                log.warn("Invalid participant type for user: {}, activeRecordId: {}, participantType: {}", userName, activityMessage.getObjectId(), participantType);
                continue;
            }
            activityUser.set("participant_types", participantType);
            updateList.add(activityUser);
        }

        if (Safes.isNotEmpty(updateList)) {
            serviceFacade.batchUpdateByFields(User.systemUser(tenantId), updateList, Lists.newArrayList("participant_types"));
            log.info("Updated participant types for {} users in active record: {}", updateList.size(), activityMessage.getObjectId());

            ActivityTagMessage activityTagMessage = new ActivityTagMessage();
            activityTagMessage.setTenantId(tenantId);
            activityTagMessage.setUserId(activityMessage.getOpId());
            activityTagMessage.setObjectId(activityMessage.getObjectId());
            activityTagMessage.setObjectApiName(activityMessage.getObjectApiName());
            paragraphProducer.sendMessage("auto_detect_participant_types", activityTagMessage);
        } else {
            log.warn("No participant types updated for active record: {}", activityMessage.getObjectId());
        }

    }

    private boolean unsupportedMessage(ActivityMessage activityMessage) {
        if (!Utils.ACTIVE_RECORD_API_NAME.equals(activityMessage.getObjectApiName())
                || Safes.isEmpty(activityMessage.getObjectId())
                || Safes.isEmpty(activityMessage.getStage())) {
            return true;
        }

        return !SUPPORT_STAGE_LIST.contains(activityMessage.getStage());
    }

    private List<IObjectData> queryActivityUserList(ActivityMessage activityMessage, String tenantId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterEq(query.getFilters(), "active_record_id", activityMessage.getObjectId());
        SearchUtil.fillFilterEq(query.getFilters(), IObjectData.IS_DELETED, 0);
        query.setLimit(AppFrameworkConfig.getMaxQueryLimit());
        return serviceFacade.findBySearchQuery(User.systemUser(tenantId), "ActivityUserObj", query).getData();
    }
}
