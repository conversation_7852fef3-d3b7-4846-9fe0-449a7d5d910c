package com.facishare.crm.task.sfa.activitysummary.service.file;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 基于 Markdown 语法结构（标题、段落）进行智能切分。
 */
@Service
public class StructuredMarkdownSplitterService implements MarkdownChunkSplitter {

    public static final String NAME = "structured";

    /** 每个块最大字符数 */
    private static final int MAX_CHUNK_SIZE = 2048;
    /** 块之间的重叠字数 */
    private static final int OVERLAP = 256;

    private static final Pattern HEADER_PATTERN = Pattern.compile("^(#{1,6}\\s+.*)$", Pattern.MULTILINE);
    private static final Pattern PARAGRAPH_PATTERN = Pattern.compile("\\n\\s*\\n");

    @Override
    public List<String> split(String markdownText) {
        List<String> chunks = new ArrayList<>();
        if (markdownText == null || markdownText.isEmpty()) {
            return chunks;
        }

        // 1. 根据标题切分文档
        List<Integer> headerPositions = new ArrayList<>();
        Matcher headerMatcher = HEADER_PATTERN.matcher(markdownText);
        while (headerMatcher.find()) {
            headerPositions.add(headerMatcher.start());
        }

        // 包含开始与结束
        headerPositions.add(0, 0);
        headerPositions.add(markdownText.length());

        for (int i = 0; i < headerPositions.size() - 1; i++) {
            int start = headerPositions.get(i);
            int end = headerPositions.get(i + 1);
            String section = markdownText.substring(start, end);
            if (section.length() <= MAX_CHUNK_SIZE) {
                chunks.add(section);
            } else {
                chunks.addAll(splitByParagraph(section));
            }
        }
        return addOverlap(chunks);
    }

    private List<String> splitByParagraph(String section) {
        List<String> chunks = new ArrayList<>();
        StringBuilder currentChunk = new StringBuilder();
        String currentHeader = "";

        Matcher headerMatcher = HEADER_PATTERN.matcher(section);
        if (headerMatcher.find() && headerMatcher.start() == 0) {
            currentHeader = headerMatcher.group();
        }

        String[] paragraphs = PARAGRAPH_PATTERN.split(section);
        for (String paragraph : paragraphs) {
            if (currentChunk.length() + paragraph.length() > MAX_CHUNK_SIZE && currentChunk.length() > 0) {
                chunks.add(currentChunk.toString());
                currentChunk = new StringBuilder(currentHeader);
            }
            if (currentChunk.length() > 0) {
                currentChunk.append("\n\n");
            }
            currentChunk.append(paragraph);
        }
        if (currentChunk.length() > 0) {
            chunks.add(currentChunk.toString());
        }
        return chunks;
    }

    /**
     * 给相邻块增加重叠内容，避免上下文丢失。
     */
    private List<String> addOverlap(List<String> originalChunks) {
        if (originalChunks.size() <= 1 || OVERLAP <= 0) {
            return originalChunks;
        }
        List<String> result = new ArrayList<>();
        for (int i = 0; i < originalChunks.size(); i++) {
            String chunk = originalChunks.get(i);
            if (i > 0) {
                String prevChunk = originalChunks.get(i - 1);
                int start = Math.max(prevChunk.length() - OVERLAP, 0);
                chunk = prevChunk.substring(start) + chunk;
            }
            result.add(chunk);
        }
        return result;
    }

    @Override
    public String getName() {
        return NAME;
    }
} 