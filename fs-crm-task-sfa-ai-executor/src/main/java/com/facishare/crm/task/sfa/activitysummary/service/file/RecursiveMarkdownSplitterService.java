package com.facishare.crm.task.sfa.activitysummary.service.file;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 递归分割 Markdown，优先使用较粗粒度的分隔符，
 * 若仍然超出限制则使用更细粒度分隔符，最后强制截断。
 */
@Service
public class RecursiveMarkdownSplitterService implements MarkdownChunkSplitter {

    public static final String NAME = "recursive";

    private static final int MAX_CHUNK_SIZE = 2048;

    private static final List<String> SEPARATORS = Arrays.asList(
            "## ", "### ", "#### ", "##### ", "###### ", // 标题
            "\n\n", "\n",                                // 段落/换行
            ". ", "! ", "? ",                             // 句子结束
            ", ", "; ", ": "                           // 子句
    );

    @Override
    public List<String> split(String text) {
        List<String> result = new ArrayList<>();
        splitRecursive(text, result);
        return result;
    }

    private void splitRecursive(String text, List<String> chunks) {
        if (text == null || text.isEmpty()) {
            return;
        }
        if (text.length() <= MAX_CHUNK_SIZE) {
            chunks.add(text);
            return;
        }
        for (String separator : SEPARATORS) {
            if (text.contains(separator)) {
                int lastIndex = findLastSeparatorIndex(text, separator);
                if (lastIndex > 0) {
                    String firstChunk = text.substring(0, lastIndex + separator.length());
                    String remaining = text.substring(lastIndex + separator.length());
                    chunks.add(firstChunk);
                    splitRecursive(remaining, chunks);
                    return;
                }
            }
        }
        // 如果没有找到合适的分隔符，强制截断
        chunks.add(text.substring(0, MAX_CHUNK_SIZE));
        splitRecursive(text.substring(MAX_CHUNK_SIZE), chunks);
    }

    private int findLastSeparatorIndex(String text, String separator) {
        int limit = MAX_CHUNK_SIZE;
        int pos = text.lastIndexOf(separator, limit);
        if (pos < 0) {
            return -1;
        }
        int minPos = (int) (limit * 0.3);
        return pos < minPos ? -1 : pos;
    }

    @Override
    public String getName() {
        return NAME;
    }
} 