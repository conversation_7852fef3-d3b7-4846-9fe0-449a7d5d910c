package com.facishare.crm.task.sfa.bizfeature.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.task.sfa.activitysummary.service.ActivityContactInsightService;
import com.facishare.crm.sfa.lto.utils.CollectionUtil;
import com.facishare.crm.task.sfa.bizfeature.constant.PrompotConstants;
import com.facishare.crm.task.sfa.bizfeature.model.FeatureInitModel;
import com.facishare.crm.task.sfa.bizfeature.service.dao.mongo.FeatureTaskInitDao;
import com.facishare.crm.task.sfa.bizfeature.service.dao.mongo.FeatureTaskInitDocument;
import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import com.facishare.crm.task.sfa.model.PAASContext;
import com.facishare.crm.task.sfa.rest.AiRestProxy;
import com.facishare.crm.task.sfa.rest.FsCrmProxy;
import com.facishare.crm.task.sfa.rest.KnowledgeSpaceProxy;
import com.facishare.crm.task.sfa.rest.PaasUserRoleProxy;
import com.facishare.crm.task.sfa.rest.dto.KnowledgeModel;
import com.facishare.crm.task.sfa.rest.dto.RoleModel;
import com.facishare.crm.task.sfa.service.impl.EnterpriseInitService;
import com.facishare.crm.task.sfa.util.ActionContextUtil;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.model.role.ChannelManagerRoleProvider;
import com.facishare.paas.appframework.privilege.model.role.Role;
import com.facishare.paas.appframework.privilege.util.Headers;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.ActionContextKey;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDescribeServiceImpl;
import com.facishare.paas.metadata.support.GDSHandler;
import com.fxiaoke.common.StopWatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class FeatureInitService {
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private ObjectDescribeServiceImpl objectDescribeService;
    @Resource
    private EnterpriseInitService enterpriseInitService;
    @Autowired
    private com.facishare.paas.appframework.privilege.RoleService roleService;
    @Resource
    private ChannelManagerRoleProvider channelManagerRoleProvider;
    @Resource
    private PaasUserRoleProxy paasUserRoleProxy;
    @Resource
    private ProfileCommonService profileCommonService;
    @Resource
    private FunctionPrivilegeService functionPrivilegeService;
    @Resource
    private ActivityContactInsightService activityContactInsightService;
    @Resource
    private FsCrmProxy fsCrmProxy;
    @Resource
    private KnowledgeSpaceProxy knowledgeSpaceProxy;
    @Resource
    protected GDSHandler gdsHandler;
    @Resource
    private AiRestProxy aiRestProxy;
    @Resource
    private FeatureTaskInitDao featureTaskInitDao;


    private static final Map<String, String> ROLE_MAP = new HashMap<String, String>() {
        {
            put("sales_portrait_agent_admin", "画像洞察Agent管理员");// ignoreI18n
            put("sales_portrait_agent_user", "画像洞察Agent使用者");// ignoreI18n
        }
    };

    private List<String> ingoreFiles = Lists.newArrayList("tenant_id", "display_name", "owner", "lock_status", "life_status", "record_type", "created_by", "create_time", "last_modified_by", "last_modified_time", "extend_obj_data_id", "package"
            , "object_describe_id", "object_describe_api_name", "version", "lock_user", "lock_rule", "life_status_before_invalid", "is_deleted", "out_tenant_id", "out_owner", "data_own_department", "data_own_organization", "data_auth_code"
            , "change_type", "out_data_auth_code", "order_by", "data_auth_id", "out_data_auth_id", "dimension_d1", "dimension_d2", "dimension_d3", "mc_currency", "mc_exchange_rate", "mc_functional_currency"
            , "mc_exchange_rate_version", "origin_source", "out_data_own_department", "out_data_own_organization", "sys_modified_time", "data_source", "system_type");

    public void initModule(FeatureInitModel param) throws MetadataServiceException, IOException {
        // 1. 查询画像及基础信息
        User user = User.systemUser(param.getTenantId());
        StopWatch stopWatch = StopWatch.createStarted("initModule" + param.getTenantId());
        //1、初始化对象描述
        initDescribe(user);
        log.info("initDescribe finish");
        stopWatch.lap("initDescribe");

        //2、初始化模板数据
        initBaseData(user);
        log.info("initBaseData finish");
        stopWatch.lap("initBaseData");
        //3、预设角色：销售教练管理员，销售教练使用者，角色与功能权限绑定关系
        addRole(user);
        log.info("addRole finish");
        stopWatch.lap("addRole");

        //4、创建知识库分类及空间
        String pId = createKnowledgeCategory(user, I18N.text("sfa.sale.agent.knowledge.category"), -15, null);
        createKnowledgeCategory(user, I18N.text("sfa.sale.agent.knowledge.group1"), -16, pId);
        createKnowledgeCategory(user, I18N.text("sfa.sale.agent.knowledge.group2"), -17, pId);
        createKnowledgeSpace(user);
        createRagIndex(user);
        log.info("createRagIndex finish");
        //5、初始化画像组件
        profileCommonService.initProfileComponent(user);
        log.info("initProfileComponent finish");
        stopWatch.lap("initProfileComponent");
        //6、创建画像信息计算定时任务
        profileCommonService.createNextTask(param.getTenantId());
        log.info("createNextTask finish");
        stopWatch.lap("createNextTask");
        //7、 特征定时任务
        FeatureTaskInitDocument taskDocument = new FeatureTaskInitDocument();
        taskDocument.setTenantId(param.getTenantId());
        featureTaskInitDao.initTask(taskDocument);
        log.info("featureTaskInit finish");
        stopWatch.lap("featureTaskInit");

        //8、联系人洞察初始话标签
        activityContactInsightService.initTags(user);
        log.info("initTags finish");
        stopWatch.logSlow(500);

    }

    private void createKnowledgeSpace(User user) {
        KnowledgeModel.CreateKnowledgeSpaceArgModel argModel = KnowledgeModel.CreateKnowledgeSpaceArgModel.builder()
                .fsUserId(Long.parseLong(user.getUserId()))
                .fsEa(gdsHandler.getEAByEI(user.getTenantId()))
                .id("SFA_SalesAgent")
                .name("SFA销售助手知识空间")// ignoreI18n
                .recordType("default__c")
                .categoryCodeList(Lists.newArrayList("-15", "-16", "-17"))
                .build();

        KnowledgeModel.CreateKnowledgeSpaceResult result =
                knowledgeSpaceProxy.createKnowledgeSpace(KnowledgeModel.CreateKnowledgeSpaceArg.builder().arg1(argModel).build()
                        , AiRestProxy.getHeaders(user.getTenantId(), user.getUserId()));
        log.info("createKnowledgeSpace_requestResult: {}", result);
    }

    private String createKnowledgeCategory(User user, String name, int code, String pId) {
        KnowledgeModel.AddKnowledgeCategoryArg arg = KnowledgeModel.AddKnowledgeCategoryArg.builder()
                .name(name)
                .record_type("default__c")
                .code(code)
                .build();
        if (!Strings.isEmpty(pId)) {
            arg.setPid(pId);
        }

        KnowledgeModel.AddKnowledgeCategoryResult result = fsCrmProxy
                .addKnowledgeCategory(arg, AiRestProxy.getHeaders(user.getTenantId(), user.getUserId()));
        log.info("createKnowledgeCategory_requestResult: {}", result);
        if (code == -15) {
            if (result != null && result.getErrCode() == 0) {
                Map<String, Object> map = result.getResult().get("result");
                pId = map.get("_id").toString();
            } else {
                SearchTemplateQueryPlus query = SearchUtil.buildBaseSearchQuery();
                SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, "code", "-15");
                List<IObjectData> knowledgeCategoryList = serviceFacade.findBySearchQueryIgnoreAll(user, "KnowledgeCategoryObj", query).getData();
                if (CollectionUtil.isNotEmpty(knowledgeCategoryList)) {
                    pId=knowledgeCategoryList.get(0).getId();
                }
            }
        }
        return pId;
    }

    private void initBaseData(User user) throws IOException {
        /* boolean flag = LicenseUtils.queryProductVersionByKey(tenantId,"key_accounts_management_industry");
        if (!flag) {
            return;
        }*/
        String[] initApiNames = {"ParseRuleObj", "ScoringRuleObj", "FeatureDimensionObj", "FeatureObj"
                , "MethodologyRuleObj", "MethodologyObj", "MethodologyNodeObj", "MethodologyTaskObj", "ObjectMethodologyObj"
                , "FeatureScoreRuleObj", "TaskFeatureObj", "NodeTaskObj", "NodeFeatureObj"
        };
        ClassLoader classLoader = getClass().getClassLoader();
        for (String apiName : initApiNames) {
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setLimit(2000);
            searchTemplateQuery.setOffset(0);
            searchTemplateQuery.setNeedReturnCountNum(false);
            searchTemplateQuery.setPermissionType(0);
            QueryResult<IObjectData> dataQueryResult = serviceFacade.findBySearchQueryIgnoreAll(user, apiName, searchTemplateQuery);

            //读取json文件初始化数据
            String jsonData = IOUtils.toString(classLoader.getResource(
                    "bizfeaturedata/init_crm_" + apiName + "_base_data.json"), "UTF-8");
            List<Map<String, Object>> jsonDataModel = JSON.parseObject(jsonData, List.class);

            List<IObjectData> addBaseDataList = new ArrayList<>();
            List<IObjectData> updateBaseDataList = new ArrayList<>();
            if (ObjectUtils.isEmpty(dataQueryResult) || ObjectUtils.isEmpty(dataQueryResult.getData())) {
                for (Map<String, Object> map : jsonDataModel) {
                    addBaseDataList.add(BuildBaseData(user, apiName, map));
                }
            } else {
                List<IObjectData> dataList = dataQueryResult.getData();
                Map<String, IObjectData> dbQueryDataMap = dataList.stream().collect(Collectors.toMap(x -> x.getId(), x -> x));

                for (Map<String, Object> map : jsonDataModel) {
                    IObjectData dbQueryData = dbQueryDataMap.get(map.get("id").toString());
                    if (dbQueryData == null) {
                        addBaseDataList.add(BuildBaseData(user, apiName, map));
                    } else {
                        if ("system".equals(dbQueryData.get("system_type", String.class))) {
                            updateBaseDataList.add(BuildUpdateBaseData(dbQueryData, map));
                        }
                    }
                }
            }

            if (!ObjectUtils.isEmpty(addBaseDataList)) {
                serviceFacade.bulkSaveObjectData(addBaseDataList, user, true, true, x -> ActionContextUtil.getSkipActionContext(user));
            }
            if (!ObjectUtils.isEmpty(updateBaseDataList)) {
                serviceFacade.bulkUpsertObjectData(updateBaseDataList, user);
            }
        }
    }

    private void initDescribe(User user) throws MetadataServiceException {
        /* boolean flag = LicenseUtils.queryProductVersionByKey(tenantId,"key_accounts_management_industry");
        if (!flag) {
            return;
        }*/
        String[] initApiNames = {"FeatureDimensionObj", "ParseRuleObj", "FeatureObj", "FeatureValueObj",
                "FeatureValueHistoryObj", "ScoringRuleObj", "MethodologyObj", "MethodologyRuleObj",
                "MethodologyNodeObj", "MethodologyTaskObj", "FeatureWeightObj", "FeatureScoreRuleObj",
                "FeatureScoreObj", "FeatureScoreHistoryObj", "MethodologyInstanceObj",
                "NodeInstanceObj", "ObjectMethodologyObj", "TaskInstanceObj", "TaskFeatureObj",
                "ProfileObj", "ProfileItemScoreObj", "ProfileProsConsObj", "ProfileAdviceObj"
                , "SalesCoachRecordObj", "NodeTaskObj", "NodeFeatureObj", "InstanceFeatureObj", "ActivityContactInsightObj"
        };
        List<IObjectDescribe> describeList = objectDescribeService.findDescribeListByApiNames(user.getTenantId(), Arrays.asList(initApiNames));
        Map<String, IObjectDescribe> describeMap = describeList.stream().collect(Collectors.toMap(IObjectDescribe::getApiName, o -> o));
        for (String apiName : initApiNames) {
            IObjectDescribe describe = describeMap.get(apiName);
            if (describe == null) {
                enterpriseInitService.initDescribeForTenant(user.getTenantId(), apiName);
            }
        }
    }

    private IObjectData BuildBaseData(User user, String apiName, Map<String, Object> jsonDataModel) {
        IObjectData baseData = new ObjectData();
        baseData.setTenantId(user.getTenantId());
        baseData.setDescribeApiName(apiName);
        baseData.setOwner(Lists.newArrayList(User.SUPPER_ADMIN_USER_ID));
        baseData.setId(jsonDataModel.get("id").toString());
        baseData.set("system_type","system");
        for (Map.Entry<String, Object> entry : jsonDataModel.entrySet()) {
            String key = entry.getKey();
            if (ingoreFiles.contains(key)) continue;
            if ("id".equals(key)) continue;

            Object value = entry.getValue();
            if (value != null) {
                String strValue = value.toString();
                if (key.equals("rule_content")) {
                    if (baseData.get("object_describe_api_name", String.class).equals("ScoringRuleObj")) {
                        baseData.set(key, JSONObject.parseArray(strValue));
                    }
                    if (baseData.get("object_describe_api_name", String.class).equals("ParseRuleObj")
                            || baseData.get("object_describe_api_name", String.class).equals("MethodologyRuleObj")) {
                        baseData.set(key, JSONObject.parseObject(strValue));
                    }
                } else if (key.equals("timer_info")) {
                    if (baseData.get("object_describe_api_name", String.class).equals("FeatureObj")) {
                        baseData.set(key, JSONObject.parseObject(strValue));
                    }
                } else {
                    if ("true".equalsIgnoreCase(strValue) || "false".equalsIgnoreCase(strValue)) {
                        baseData.set(key, Boolean.parseBoolean(strValue));
                    } else {
                        baseData.set(key, value);
                    }
                }
            }
        }
        return baseData;
    }
    private IObjectData BuildUpdateBaseData(IObjectData baseData, Map<String, Object> jsonDataModel) {
        for (Map.Entry<String, Object> entry : jsonDataModel.entrySet()) {
            String key = entry.getKey();
            if (ingoreFiles.contains(key)) continue;
            if ("id".equals(key)) continue;

            Object value = entry.getValue();
            if (value != null) {
                if (value instanceof String) {
                    String strValue = value.toString();
                    if (key.equals("rule_content")) {
                        if (baseData.get("object_describe_api_name", String.class).equals("ScoringRuleObj")) {
                            baseData.set(key, JSONObject.parseArray(strValue));
                        }
                        if (baseData.get("object_describe_api_name", String.class).equals("ParseRuleObj")
                                || baseData.get("object_describe_api_name", String.class).equals("MethodologyRuleObj")) {
                            baseData.set(key, JSONObject.parseObject(strValue));
                        }
                    } else if (key.equals("timer_info")) {
                        if (baseData.get("object_describe_api_name", String.class).equals("FeatureObj")) {
                            baseData.set(key, JSONObject.parseObject(strValue));
                        }
                    } else {
                        if ("true".equalsIgnoreCase(strValue) || "false".equalsIgnoreCase(strValue)) {
                            baseData.set(key, Boolean.parseBoolean(strValue));
                        } else {
                            baseData.set(key, value);
                        }
                    }
                } else {
                    baseData.set(key, value);
                }
            }
        }
        baseData.set("system_type","system");
        return baseData;
    }

    /**
     * 添加渠道经理角色
     *
     * @param user
     * @return
     */
    public void addRole(User user) {
        for (Map.Entry<String,String> entry : ROLE_MAP.entrySet()) {
            if (Boolean.TRUE.equals(roleService.checkRoleIsExist(user.getTenantId(), "CRM", entry.getKey()))) {
                return;
            }
            RoleModel.AddRoleArg addRoleArg = RoleModel.AddRoleArg.builder()
                    .roleCode(entry.getKey())
                    .roleName(entry.getValue())
                    .roleType(Role.RoleType.DEFAULT_ROLE.getType())
                    .groupCode("AISalesAssistantRoleGroup")
                    .description(entry.getValue())
                    //.licenseCode("user_license_ai_portrait_insight_agent_user_limit")
                    .authContext(PAASContext.builder().appId("CRM").tenantId(user.getTenantId()).userId(user.getUserId()).build())
                    .build();

            RoleModel.AddRoleResult addRoleResult = paasUserRoleProxy
                    .addRole(addRoleArg, Headers.PAAS_PRIVILEGE_HEADDER.buildHeader(user.getTenantId()));
            log.info("addRoleResult:{}", addRoleResult);

            //绑定角色功能权限
            functionPrivilegeService.createFuncCode(user, Utils.ACCOUNT_API_NAME, "sfa_ai_profile", "AI画像");//ignoreI18n
            functionPrivilegeService.updateUserDefinedFuncAccess(user, entry.getKey(), Utils.ACCOUNT_API_NAME, Lists.newArrayList("sfa_ai_profile"), Lists.newArrayList());
            functionPrivilegeService.createFuncCode(user, Utils.LEADS_API_NAME, "sfa_ai_profile", "AI画像");//ignoreI18n
            functionPrivilegeService.updateUserDefinedFuncAccess(user, entry.getKey(), Utils.LEADS_API_NAME, Lists.newArrayList("sfa_ai_profile"), Lists.newArrayList());
            functionPrivilegeService.createFuncCode(user, Utils.NEW_OPPORTUNITY_API_NAME, "sfa_ai_profile", "AI画像");//ignoreI18n
            functionPrivilegeService.updateUserDefinedFuncAccess(user, entry.getKey(), Utils.NEW_OPPORTUNITY_API_NAME, Lists.newArrayList("sfa_ai_profile"), Lists.newArrayList());

        }
    }
    private void createRagIndex(User user) {
        String ragApiName = PrompotConstants.RAG_KNOWLEDGE_USER;
        AiRestProxyModel.CreateRagArg  arg = new AiRestProxyModel.CreateRagArg();
        arg.setApiName(ragApiName);
        arg.setBindingApiName("ServiceKnowledgeObj");
        arg.setDataSourceType("object");
        arg.setDescribe("企业流程建议知识库索引");// ignoreI18n
        arg.setName("企业流程建议知识库索引");// ignoreI18n
        arg.setSearchQueryInfo("{\"filters\":[{\"field_name\":\"category\",\"field_values\":[\"-15\",\"-17\"],\"filterGroup\":\"1\",\"operator\":\"IN\"},{\"field_name\":\"public_status\",\"field_values\":\"1\",\"filterGroup\":\"1\",\"operator\":\"EQ\"}]}");
        AiRestProxyModel.QueryStructure queryStructure = new AiRestProxyModel.QueryStructure();
        Map<String, String> title = Maps.newHashMap();
        title.put("fieldName", "title");
        List<Map<String, String>> content = Lists.newArrayList();
        Map<String, String> content1 = Maps.newHashMap();
        content1.put("fieldName", "material_message");
        Map<String, String> content2 = Maps.newHashMap();
        content2.put("fieldName", "text");
        Map<String, String> content3 = Maps.newHashMap();
        content3.put("fieldName", "rich_text");
        content.add(content1);
        content.add(content2);
        content.add(content3);
        queryStructure.setTitle(title);
        queryStructure.setContent(content);
        arg.setFieldMapping(queryStructure);

        AiRestProxyModel.RagResposne result = aiRestProxy.create(arg, AiRestProxy.getHeaders(user.getTenantId(), user.getUserId()));
        log.info("createRagIndex result:{}", result);
    }
}
