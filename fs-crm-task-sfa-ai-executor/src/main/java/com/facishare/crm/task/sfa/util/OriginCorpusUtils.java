package com.facishare.crm.task.sfa.util;

import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class OriginCorpusUtils {


    public static String makeUpCorpusOriginText(List<InteractiveDocument> documents, List<IObjectData> activityUserList) {

        Map<String, String> nameMap = activityUserList.stream().collect(
                Collectors.toMap(IObjectData::getId,
                        d -> d.get("user_name", String.class))
        );

        StringBuilder sb = new StringBuilder();
        for (InteractiveDocument document : documents) {
            String activityUserId = document.getActivityUserId();
            String userName = nameMap.get(activityUserId);
            sb.append(document.getSeq()).append(".");
            sb.append(userName).append(":");
            sb.append(document.getContent()).append("\n");
        }

        return sb.toString();
    }
}
