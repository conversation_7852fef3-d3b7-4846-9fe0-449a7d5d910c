package com.facishare.crm.task.sfa.activitysummary.consumer;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.activitysummary.service.ActivityInteractionService;
import com.facishare.crm.task.sfa.common.gray.GrayUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

@Component
@Slf4j

public class ActivityInteractiveListener extends AbstractActivityCommonListener {

    @Resource
    private ActivityInteractionService activityInteractionService;

    @Override
    String getSection() {
        return "sfa-ai-activity-Interactive-topics";
    }

    @Override
    void consume(ActivityMessage activityMessage) {
        String stage = activityMessage.getStage();
        if (ObjectUtils.isEmpty(stage)) {
            return;
        }
        
        log.info("ActivityInteractiveListener activityMessage:{}",JSONObject.toJSONString(activityMessage));
        if (GrayUtils.isSkipActivityInteraction(activityMessage.getTenantId())) {
            log.warn("ActivityInteractiveListener isSkipActivityInteraction {}",activityMessage.getTenantId());
            return;
        }
        switch (stage) {
            case "AddNoAttachment":  // 无附件新增
                activityInteractionService.handleActivityInteraction(activityMessage, false, false);
                break;
//            case "AddNoAttachmentButHaveContext":  // 无附件但是context有值新增
//                activityInteractionService.handleActivityInteraction(activityMessage, false, false);
//                break;
            case "file2text":  // 附件转文本
                activityInteractionService.handleActivityInteraction(activityMessage, false, false);
                break;
            case "realtime2text":  // 实时音频转文字
                ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
                parallelTask.submit(() -> {
                    activityInteractionService.handleActivityInteraction(activityMessage, true, false);
                });
                parallelTask.run();
                break;
            case "realtime2textDone":// 实时音频转文字(全部结束时)
                activityInteractionService.handleActivityInteraction(activityMessage, true,true);
                break;
        }
    }
}
