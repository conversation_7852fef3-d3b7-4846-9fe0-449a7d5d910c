package com.facishare.crm.task.sfa.activitysummary.service;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static com.facishare.crm.task.sfa.common.constants.CommonConstant.INTERACTIVE_CONTENT;

/**
 * 历史附件解析服务
 * 用于批量处理历史ActiveRecordObj中的附件
 */
@Slf4j
@Service
public class AttachmentRebuildService {

    @Resource
    private ServiceFacade serviceFacade;
    
    @Resource
    private AttachmentStrategy attachmentStrategy;
    
    @Resource
    private RecordingTaskService recordingTaskService;
    
    private static final int PAGE_SIZE = 500; // 每页处理数量
    
    /**
     * 处理历史附件
     * @param tenantIds 租户ID列表
     * @param startDate 开始日期 yyyy-MM-dd
     * @param endDate 结束日期 yyyy-MM-dd
     * @return 处理的记录数
     */
    public int processHistoricalAttachments(List<String> tenantIds, String startDate, String endDate) {
        log.info("Start processing historical attachments for tenants: {}, startDate: {}, endDate: {}", 
                tenantIds, startDate, endDate);
        
        AtomicInteger totalProcessedCount = new AtomicInteger(0);
        
        for (String tenantId : tenantIds) {
            try {
                log.info("Processing tenant: {}", tenantId);
                int tenantProcessedCount = processAttachmentsForTenant(tenantId, startDate, endDate);
                totalProcessedCount.addAndGet(tenantProcessedCount);
                log.info("Completed processing for tenant: {}, processed: {}", tenantId, tenantProcessedCount);
            } catch (Exception e) {
                log.error("Error processing historical attachments for tenant: {}", tenantId, e);
                // 继续处理其他租户，不中断整个流程
            }
        }
        
        log.info("Completed processing historical attachments for all tenants. Total processed: {}", 
                totalProcessedCount.get());
        return totalProcessedCount.get();
    }
    
    /**
     * 处理单个租户的历史附件
     */
    private int processAttachmentsForTenant(String tenantId, String startDate, String endDate) {
        log.info("Start processing historical attachments for tenant: {}, startDate: {}, endDate: {}", 
                tenantId, startDate, endDate);
        
        User user = User.systemUser(tenantId);
        AtomicInteger processedCount = new AtomicInteger(0);
        int offset = 0;
        
        try {
            while (true) {
                // 搜索含有附件的ActiveRecordObj，每次搜索500条
                List<IObjectData> records = searchRecordsWithAttachments(user, offset, PAGE_SIZE, startDate, endDate);
                
                if (CollectionUtils.isEmpty(records)) {
                    log.info("No more records to process for tenant: {}, total processed: {}", tenantId, processedCount.get());
                    break;
                }
                
                log.info("Processing batch for tenant: {} at offset: {}, records found: {}", tenantId, offset, records.size());
                
                // 批量处理记录
                for (IObjectData record : records) {
                    try {
                        processAttachment(tenantId, record);
                        processedCount.incrementAndGet();
                    } catch (Exception e) {
                        log.error("Failed to process attachment for record: {} in tenant: {}", record.getId(), tenantId, e);
                    }
                }
                
                // 如果返回的记录数少于PAGE_SIZE，说明已经是最后一页
                if (records.size() < PAGE_SIZE) {
                    log.info("Reached last page for tenant: {}, processed all records", tenantId);
                    break;
                }
                
                offset += PAGE_SIZE;
                
                // 添加延迟，避免对系统造成过大压力
                Thread.sleep(1000);
            }
            
        } catch (Exception e) {
            log.error("Error processing historical attachments for tenant: {}", tenantId, e);
            throw new RuntimeException("Failed to process historical attachments for tenant: " + tenantId, e);
        }
        
        log.info("Completed processing historical attachments for tenant: {}. Total processed: {}", tenantId, processedCount.get());
        return processedCount.get();
    }
    
    /**
     * 搜索含有附件的ActiveRecordObj
     */
    private List<IObjectData> searchRecordsWithAttachments(User user, int offset, int limit, 
                                                           String startDate, String endDate) {
        List<IFilter> filters = new ArrayList<>();
        
        // 必须有附件
        SearchUtil.fillFilterISNotNull(filters, "interaction_records");
        SearchUtil.fillFilterNotEq(filters, "interaction_records", "[]");
        
        // interactive_render_types 必须包含 corpus_text
        SearchUtil.fillFilterIn(filters, "interactive_render_types", Lists.newArrayList("corpus_text"));
        
        // 日期范围过滤
        if (startDate != null && endDate != null) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                Date start = sdf.parse(startDate);
                Date end = sdf.parse(endDate);
                
                SearchUtil.fillFilterGTE(filters, DBRecord.CREATE_TIME, start.getTime());
                SearchUtil.fillFilterLTE(filters, DBRecord.CREATE_TIME, end.getTime());
            } catch (Exception e) {
                log.warn("Invalid date format, ignoring date filter", e);
            }
        }
        
        // 排除已经处理过的（可以通过某个字段标记，比如附件解析状态）
        // 这里假设有一个字段 attachment_parsed 标记是否已解析
        // SearchUtil.fillFilterNotEq(filters, "attachment_parsed", true);
        
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filters);
        query.setOffset(offset);
        query.setLimit(limit);
        query.setPermissionType(0); // 系统权限
        
        try {
            List<IObjectData> result = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(
                user, 
                Utils.ACTIVE_RECORD_API_NAME, 
                query,
                Lists.newArrayList(
                    DBRecord.ID,
                    "interaction_records",
                    INTERACTIVE_CONTENT,
                    "interactive_render_types"
                )
            ).getData();
            
            log.info("Found {} records with attachments at offset {}", 
                    result != null ? result.size() : 0, offset);
            
            return result != null ? result : Collections.emptyList();
        } catch (Exception e) {
            log.error("Error searching records with attachments", e);
            return Collections.emptyList();
        }
    }
    
    /**
     * 处理单个记录的附件
     */
    private void processAttachment(String tenantId, IObjectData record) {
        List<Map<String, Object>> interactionRecords = (List<Map<String, Object>>) record.get("interaction_records");
        
        if (CollectionUtils.isEmpty(interactionRecords)) {
            return;
        }

        try {
            Thread.sleep(30);
        } catch (InterruptedException e) {
            log.warn("Sleep interrupted while processing record: {}", record.getId(), e);
            Thread.currentThread().interrupt(); // 恢复中断状态
        }

        // 获取创建者信息
        List<String> createdBy = record.get(DBRecord.CREATED_BY, List.class);
        String opId = CollectionUtils.isNotEmpty(createdBy) ? createdBy.get(0) : "-10000";
        
        // 构建ActivityMessage
        ActivityMessage message = ActivityMessage.builder()
                .tenantId(tenantId)
                .objectId(record.getId())
                .objectApiName(Utils.ACTIVE_RECORD_API_NAME)
                .stage("Add")
                .actionCode("Add")
                .opId(opId)
                .build();
        
        log.info("Processing attachment for record: {}, tenantId: {}", record.getId(), tenantId);
        
        // 调用现有的附件处理策略
        attachmentStrategy.handleFile(message);
    }
}