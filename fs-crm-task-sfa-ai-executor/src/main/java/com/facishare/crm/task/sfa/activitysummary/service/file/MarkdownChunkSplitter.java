package com.facishare.crm.task.sfa.activitysummary.service.file;

import java.util.List;

/**
 * Markdown 文本分块器接口。
 * 每个实现类负责按照自身策略将 Markdown 字符串切分为若干片段。
 *
 * <AUTHOR>
 */
public interface MarkdownChunkSplitter {

    /**
     * 将输入的 Markdown 字符串切分为片段。
     *
     * @param markdownText Markdown 文本
     * @return 切分后的片段列表
     */
    List<String> split(String markdownText);

    /**
     * 返回当前分块器在策略上下文中的唯一名称。
     */
    String getName();
} 