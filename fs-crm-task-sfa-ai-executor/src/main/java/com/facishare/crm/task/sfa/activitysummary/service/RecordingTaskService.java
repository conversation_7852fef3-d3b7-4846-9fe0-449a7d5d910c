package com.facishare.crm.task.sfa.activitysummary.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.http.FormatType;
import com.aliyuncs.http.HttpClientConfig;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.http.ProtocolType;
import com.aliyuncs.profile.DefaultProfile;
import com.facishare.crm.sfa.lto.activity.mongo.ActivityMongoDao;
import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.sfa.lto.activity.producer.ActivityRocketProducer;
import com.facishare.crm.sfa.lto.activity.service.ActivityResourceUsageService;
import com.facishare.crm.sfa.lto.utils.HttpHeaderUtil;
import com.facishare.crm.sfa.lto.utils.ProxyConfigUtil;
import com.facishare.crm.task.sfa.activitysummary.enums.FileTypeEnum;
import com.facishare.crm.task.sfa.activitysummary.model.ActivityTaskMessage;
import com.facishare.crm.task.sfa.procurement.service.NomonTask;
import com.facishare.crm.task.sfa.rest.FsBigFileManagerProxy;
import com.facishare.crm.task.sfa.rest.StoneAuthProxy;
import com.facishare.crm.task.sfa.rest.dto.StoneAuthModels;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.support.GDSHandler;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> gongchunru
 * @date : 2025/1/2 18:51
 * @description: 实时录音中，结果轮询处理。
 */
@Slf4j
@Service
public class RecordingTaskService {

    private static final String TINGWU_DOMAIN = "tingwu.cn-beijing.aliyuncs.com";
    private static final String TINGWU_VERSION = "2023-09-30";
    private static final String TINGWU_REGION = "cn-beijing";

    @Autowired
    private StoneAuthProxy stoneAuthProxy;

    @Resource
    protected GDSHandler gdsHandler;

    @Resource
    private NomonTask nomonTask;

    @Resource
    private FsBigFileManagerProxy fsBigFileManagerProxy;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private ActivityRocketProducer activityRocketProducer;

    @Resource
    private InteractiveScenarioService interactiveScenarioService;

    @Autowired
    private AudioAttachment audioAttachment;

    @Autowired
    private ActivityResourceUsageService activityResourceUsageService;

    @Autowired
    private ActivityMongoDao activityMongoDao;

    public void processRecordingTask(ActivityTaskMessage.Rec2TextTask rec2TextTask) {
            StoneAuthModels.TingWuCredentialData credentialData = getTingWuCredential(rec2TextTask.getTenantId());
            String queryUrl = String.format("/openapi/tingwu/v2/tasks/%s", rec2TextTask.getTaskId());

            // 创建请求和客户端
            CommonRequest request = createCommonRequest(queryUrl, MethodType.GET);
            IAcsClient client = createAcsClient(credentialData);

            try {
                // 发送请求获取结果
                CommonResponse response = client.getCommonResponse(request);
                log.info("Task info response: taskId={}, response={}", rec2TextTask.getTaskId(), response.getData());

                JSONObject jsonResponse = JSON.parseObject(response.getData());
                JSONObject data = jsonResponse.getJSONObject("Data");
                String taskStatus = data.getString("TaskStatus");

                if ("COMPLETED".equals(taskStatus) || StringUtils.isNotBlank(data.getString("OutputMp3Path"))) {
                    String outputMp3Path = data.getString("OutputMp3Path");
                    handleCompletedTask(rec2TextTask, outputMp3Path);
                } else {
                    handleInProgressTask(rec2TextTask);
                }
            } catch (Exception e) {
                log.error("Get task info failed, taskId: {}, error: ", rec2TextTask.getTaskId(), e);
                throw new RuntimeException(e);
            }

    }

    private void handleInProgressTask(ActivityTaskMessage.Rec2TextTask rec2TextTask) {
        // 检查任务是否超时 (4 小时)
        long taskCreateTime = rec2TextTask.getCreateTime();
        if (System.currentTimeMillis() - taskCreateTime > 4 * 3600 * 1000) {
            stopRecordingTask(rec2TextTask, rec2TextTask.getTaskId());
            log.info("Task timeout and stopped, taskId: {}", rec2TextTask.getTaskId());
            return;
        }
        // 任务未完成，发送 MQ 消息，2 分钟后执行
        nomonTask.sendActivityTextTask(rec2TextTask);
        log.info("Send MQ message success for taskId: {}", rec2TextTask.getTaskId());
    }

    private void handleCompletedTask(ActivityTaskMessage.Rec2TextTask rec2TextTask, String outputMp3Path) {
        log.info("Task is completed, taskId: {}", rec2TextTask.getTaskId());
        interactiveScenarioService.checkInteractiveScenario(rec2TextTask.getTaskId(), rec2TextTask.getObjectId());
        sendActivityMessage(rec2TextTask);
        if (outputMp3Path == null) {
            log.warn("handleCompletedTask none path,taskId:{},objectId:{}", rec2TextTask.getTaskId(),rec2TextTask.getObjectId());
            updateNoFileUsage(rec2TextTask);
            return;
        }
        // String fileName = outputMp3Path.substring(outputMp3Path.lastIndexOf("/") + 1);
        // 使用日期时间格式作为文件名，格式:yyyyMMddHHmmssSSS.mp3
        String fileName = new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date()) + ".mp3";
        String objectKey = outputMp3Path.substring(outputMp3Path.indexOf("tingwu/"));
        // 获取任务结果的 npath
        StoneAuthModels.filePathData filePathData = getPath(rec2TextTask, fileName, objectKey);
        log.info("handleCompletedTask filePathData: {}", filePathData);
        updateActivityPath(rec2TextTask.getTenantId(), rec2TextTask.getObjectId(), rec2TextTask.getObjectApiName(), filePathData, fileName);
        updateResourceUsage0(rec2TextTask.getTenantId(), rec2TextTask.getOpId(), rec2TextTask.getObjectId(), rec2TextTask.getObjectApiName(), filePathData.getPath());
    }

    public Map<String, String> stopRecordingTask(ActivityTaskMessage.Rec2TextTask rec2TextTask, String taskId) {
        try {
            StoneAuthModels.TingWuCredentialData credentialData = getTingWuCredential(rec2TextTask.getTenantId());

            // 创建请求
            CommonRequest request = createCommonRequest("/openapi/tingwu/v2/tasks", MethodType.PUT);

            request.putQueryParameter("type", "realtime");
            request.putQueryParameter("operation", "stop");

            // 构建请求体
            JSONObject root = new JSONObject();
            JSONObject input = new JSONObject();
            input.put("TaskId", taskId);
            root.put("Input", input);

            // 设置请求内容
            request.setHttpContent(root.toJSONString().getBytes(), "utf-8", FormatType.JSON);

            // 发送请求
            IAcsClient client = createAcsClient(credentialData);
            CommonResponse response = client.getCommonResponse(request);

            String queryUrl = String.format("/openapi/tingwu/v2/tasks/%s", taskId);
            // 停止任务后，再次获取任务结果
            CommonRequest getRequest = createCommonRequest(queryUrl, MethodType.GET);
            response = client.getCommonResponse(getRequest);
            log.info("Get task result after stop: taskId={}, response={}", taskId, response.getData());

            JSONObject jsonResponse = JSON.parseObject(response.getData());
            JSONObject data = jsonResponse.getJSONObject("Data");
            String taskStatus = data.getString("TaskStatus");

            if ("COMPLETED".equals(taskStatus)) {
                String outputMp3Path = data.getString("OutputMp3Path");
                if (outputMp3Path != null) {
                    handleCompletedTask(rec2TextTask, outputMp3Path);
                } else {
                    updateNoFileUsage(rec2TextTask);
                    nomonTask.sendActivityTextTask(rec2TextTask);
                }
            }

            // 返回结果
            Map<String, String> result = new HashMap<>();
            result.put("status", "success");
            return result;

        } catch (Exception e) {
            log.error("停止实时转写任务失败", e);
            throw new ValidateException("停止实时转写任务失败：" + e.getMessage());// ignoreI18n
        }
    }

    private StoneAuthModels.TingWuCredentialData getTingWuCredential(String tenantId) {
        Map<String, String> headers = new HashMap<>();
        Map<String, String> pathParams = new HashMap<>();
        pathParams.put("ea", gdsHandler.getEAByEI(tenantId));

        StoneAuthModels.GetTingWuCredentialResponse tingWuCredential = stoneAuthProxy.getTingWuCredential(headers, pathParams);
        if (!tingWuCredential.getSuccess()) {
            throw new ValidateException("Failed to get TingWu credential");
        }
        return tingWuCredential.getData();
    }

    private IAcsClient createAcsClient(StoneAuthModels.TingWuCredentialData credentialData) {
        DefaultProfile profile = DefaultProfile.getProfile(
                TINGWU_REGION,
                credentialData.getAccessKey(),
                credentialData.getSecretKey(),
                credentialData.getStsToken()
        );
        HttpClientConfig clientConfig = HttpClientConfig.getDefault();
        clientConfig.setHttpProxy("http://" + ProxyConfigUtil.getProxyHost() + ":" + ProxyConfigUtil.getProxyPort());
        clientConfig.setHttpsProxy("http://" + ProxyConfigUtil.getProxyHost() + ":" + ProxyConfigUtil.getProxyPort());
        profile.setHttpClientConfig(clientConfig);
        return new DefaultAcsClient(profile);
    }

    private CommonRequest createCommonRequest(String uri, MethodType method) {
        CommonRequest request = new CommonRequest();
        request.setSysDomain(TINGWU_DOMAIN);
        request.setSysVersion(TINGWU_VERSION);
        request.setSysProtocol(ProtocolType.HTTPS);
        request.setSysMethod(method);
        request.setSysUriPattern(uri);
        request.setHttpContentType(FormatType.JSON);
        return request;
    }

    private StoneAuthModels.filePathData getPath(ActivityTaskMessage.Rec2TextTask rec2TextTask, String fileName, String objectKey) {
        Map<String, String> headers = HttpHeaderUtil.getHeaders(new User(rec2TextTask.getTenantId(), rec2TextTask.getOpId()));
        Map<String, Object> bodyParams = new HashMap<>();
        bodyParams.put("employeeAccount", gdsHandler.getEAByEI(rec2TextTask.getTenantId()));
        bodyParams.put("employeeId", rec2TextTask.getOpId());
        bodyParams.put("fileName", fileName);
        bodyParams.put("business", "sfa");
        bodyParams.put("fileExpireDay", 0);
        bodyParams.put("objectKey", objectKey);
        bodyParams.put("specialBusinessKey", "SFA_ACTIVITY_SPECIAL");
        StoneAuthModels.GenerationPathByObjectKeyResponse generationPathByObjectKeyResponse = fsBigFileManagerProxy.generationPathByObjectKey(headers, bodyParams);
        if (generationPathByObjectKeyResponse.isSuccess()) {
            return generationPathByObjectKeyResponse.getData();
        }
        return null;
    }


    /**
     * 更新活动文本
     *
     * @param tenantId      租户 ID
     * @param objectId      对象 ID
     * @param objectApiName 对象 API 名称
     * @param path          文件路径
     * @param fileName      文件名称
     */
    public void updateActivityPath(String tenantId, String objectId, String objectApiName, StoneAuthModels.filePathData filePathData, String fileName) {
        IActionContext context = new ActionContext();
        context.setEnterpriseId(tenantId);
        context.setUserId("-10000");
        IObjectData data = new ObjectData();
        data.setTenantId(tenantId);
        data.setId(objectId);
        data.setDescribeApiName(objectApiName);
        List<Map<String, Object>> interactionRecords = new ArrayList<>();
        Map<String, Object> record = new HashMap<>();
        record.put("ext", "mp3");
        record.put("path", filePathData.getPath());
        record.put("filename", fileName);
        record.put("create_time", System.currentTimeMillis());
        record.put("size", filePathData.getFileSize());
        interactionRecords.add(record);
        data.set("interaction_records", JSON.toJSONString(interactionRecords));
        Map<String, Object> updateMap = new HashMap<>();
        updateMap.put("interaction_records", interactionRecords);
        try {
            serviceFacade.batchUpdateWithMap(new User(tenantId, "-10000"), Lists.newArrayList(data), updateMap);
        } catch (Exception e) {
            log.error("更新活动文本失败，tenantId:{}, objectId:{}, objectApiName:{}", tenantId, objectId, objectApiName, e);
        }
    }

    protected void sendActivityMessage(ActivityTaskMessage.Rec2TextTask rec2TextTask) {
        ActivityMessage message = ActivityMessage.builder()
                .tenantId(rec2TextTask.getTenantId())
                .objectId(rec2TextTask.getObjectId())
                .objectApiName(rec2TextTask.getObjectApiName())
                .stage("realtime2textDone")
                .actionCode("realtime2textDone")
                .language(rec2TextTask.getLanguage())
                .opId(rec2TextTask.getOpId())
                .build();
        activityRocketProducer.sendActivityToTextMessage(message);
    }

    public void updateResourceUsage(String tenantId, String userId, String dataId, String dataApiName) {
        List<IObjectData> dataList = serviceFacade.findObjectDataByIds(tenantId, Collections.singletonList(dataId), dataApiName);
        if (dataList.isEmpty()) {
            return;
        }
        IObjectData data = dataList.get(0);
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> interactionRecords = (List<Map<String, Object>>) data.get("interaction_records");
        if (interactionRecords == null || interactionRecords.isEmpty()) {
            log.warn("[u resource usage {}] interaction records is empty", data.getId());
            return;
        }
        Map<String, Object> interactionRecord = interactionRecords.get(0);
        FileTypeEnum fileType = FileTypeEnum.getByCode(String.valueOf(interactionRecord.get("ext")));
        if (fileType != null && audioAttachment.support(fileType)) {
            updateResourceUsage0(tenantId, userId, dataId, data.getDescribeApiName(), String.valueOf(interactionRecord.get("path")));
        } else {
            log.warn("[u resource usage {}] audio attachment not support", data.getId());
        }
    }

    private void updateResourceUsage0(String tenantId, String userId, String dataId, String dataApiName, String path) {
        try {
            String ea = serviceFacade.getEAByEI(tenantId);
            long usage = parseUsageFromFile(ea, userId, path);
            activityResourceUsageService.updateZeroUsage(tenantId, dataId, dataApiName, usage);
        } catch (Exception e) {
            log.error("[u resource(file) usage {}] error ", dataId, e);
        }
    }

    private long parseUsageFromFile(String ea, String userId, String filePath) {
        StoneAuthModels.AudioInfo result = fsBigFileManagerProxy.getAudioInfo(ea, userId, filePath);
        if (result.getCode() != 200) {
            throw new RuntimeException(result.getMessage());
        } else {
            return Optional.ofNullable(result.getData()).map(StoneAuthModels.AudioData::getDuration).map((num) -> num.setScale(0, RoundingMode.HALF_UP)).map(BigDecimal::longValue).orElse(0L);
        }
    }

    private void updateNoFileUsage(ActivityTaskMessage.Rec2TextTask task) {
        String tenantId = task.getTenantId();
        String dataId = task.getObjectId();
        String dataApiName = task.getObjectApiName();
        try {
            InteractiveDocument document = activityMongoDao.queryLargestSeqDocument(tenantId, dataApiName, dataId);
            if (document != null) {
                Duration duration = ActivityResourceUsageService.parseDuration(document.getEndTime() == null ? document.getStartTime() : document.getEndTime());
                activityResourceUsageService.updateZeroUsage(tenantId, dataId, dataApiName, TimeUnit.MILLISECONDS.toSeconds(duration.toMillis()));
            }
        } catch (Exception e) {
            log.error("[u resource(no file) usage {}] error ", dataId, e);
        }
    }
}
