package com.facishare.crm.task.sfa.activitysummary.service.file;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 集中处理 Markdown 文本切分的服务。
 * <p>
 * 内部维护若干 {@link MarkdownChunkSplitter} 实现，并提供统一的 {@link #split(String)} 方法。
 * 通过简单的函数规则自动选择最佳的分块策略。
 * </p>
 */
@Service
public class MarkdownChunkStrategyService {

    private final Map<String, MarkdownChunkSplitter> splitterMap = new HashMap<>();

    @Autowired
    public MarkdownChunkStrategyService(List<MarkdownChunkSplitter> splitterList) {
        for (MarkdownChunkSplitter splitter : splitterList) {
            splitterMap.put(splitter.getName(), splitter);
        }
    }

    /**
     * 根据内置规则，自动选择分块策略并切分文本。
     *
     * @param markdownText Markdown 文本
     * @return 分块结果
     */
    public List<String> split(String markdownText) {
        String strategy = decideStrategy(markdownText);
        MarkdownChunkSplitter splitter = splitterMap.get(strategy);
        if (Objects.isNull(splitter)) {
            // 兜底策略
            splitter = splitterMap.get(SimpleMarkdownSplitterService.NAME);
        }
        return splitter.split(markdownText);
    }

    /**
     * 简单的函数规则：
     * 1. 文本长度超过 8000 字符时，使用递归分割。
     * 2. 若包含 Markdown 标题("#")，使用结构化分割。
     * 3. 否则使用简单分割。
     */
    private String decideStrategy(String markdownText) {
        if (markdownText == null) {
            return SimpleMarkdownSplitterService.NAME;
        }
        if (markdownText.length() > 8000) {
            return RecursiveMarkdownSplitterService.NAME;
        }
        if (markdownText.contains("#")) {
            return StructuredMarkdownSplitterService.NAME;
        }
        return SimpleMarkdownSplitterService.NAME;
    }
} 