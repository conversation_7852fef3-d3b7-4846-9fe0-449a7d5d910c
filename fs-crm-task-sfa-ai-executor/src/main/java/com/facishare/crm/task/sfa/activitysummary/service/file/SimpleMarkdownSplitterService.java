package com.facishare.crm.task.sfa.activitysummary.service.file;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 基于固定字符长度切分 Markdown 的简单实现。
 */
@Service
public class SimpleMarkdownSplitterService implements MarkdownChunkSplitter {

    public static final String NAME = "simple";
    /** 默认块大小 */
    private static final int DEFAULT_CHUNK_SIZE = 1024;
    /** 默认重叠字符数 */
    private static final int DEFAULT_OVERLAP = 128;

    private final int chunkSize;
    private final int overlap;

    public SimpleMarkdownSplitterService() {
        this(DEFAULT_CHUNK_SIZE, DEFAULT_OVERLAP);
    }

    public SimpleMarkdownSplitterService(int chunkSize, int overlap) {
        this.chunkSize = chunkSize;
        this.overlap = overlap;
    }

    @Override
    public List<String> split(String markdownText) {
        List<String> chunks = new ArrayList<>();
        if (markdownText == null || markdownText.isEmpty()) {
            return chunks;
        }
        int position = 0;
        while (position < markdownText.length()) {
            int end = Math.min(position + chunkSize, markdownText.length());
            chunks.add(markdownText.substring(position, end));
            position = end - overlap;
            if (position < 0) {
                position = 0;
            }
        }
        return chunks;
    }

    @Override
    public String getName() {
        return NAME;
    }
} 