package com.facishare.crm.task.sfa.activitysummary.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.audit.log.SFAAuditLog;
import com.facishare.crm.sfa.lto.activity.mongo.ActivityMongoDao;
import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.sfa.lto.utils.LicenseCheckUtil;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.crm.task.sfa.activitysummary.model.CRMFeedConstants;
import com.facishare.crm.task.sfa.activitysummary.model.InteractiveScenarioModel;
import com.facishare.crm.task.sfa.activitysummary.model.auditlog.ActivityMessageConverter;
import com.facishare.crm.task.sfa.common.constants.CommonConstant;
import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import com.facishare.crm.task.sfa.util.I18NKey;
import com.facishare.crm.task.sfa.util.InheritRecordUtil;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.common.StopWatch;
import com.fxiaoke.i18n.client.I18nClient;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.facishare.paas.metadata.api.MultiRecordType.RECORD_TYPE_DEFAULT;

@Slf4j
@Component
public class ActivityMeetingSummaryServiceImpl {
    @Autowired
    private ServiceFacade serviceFacade;

    @Resource
    private IObjectDataService objectDataService;

    @Resource
    private InteractiveScenarioService interactiveScenarioService;

    @Resource
    private CompletionsService completions;

    @Autowired
    private ActivityMongoDao activityMongoDao;

    @Resource
    ActivityUserService activityUserService;

    static String SOURCE_TEXT_FIELD_NAME = "interactive_content";

    @Data
    @Builder
    static class ActivityMeetingSummaryContext {
        private String title;
        private String lineSymbol;
        private String fieldName;
        private String apiName;
        private String describeId;
        private Map<String, String> promptApiNameMap;
        private Map<String, String> aiResponseMap;
        /** 是否区分互动场景 */
        private Boolean enableInteractiveScenario;
    }

    static {
        ConfigFactory.getInstance().getConfig("fs-crm-sales-config", config -> {
            SOURCE_TEXT_FIELD_NAME = config.get("activity_meeting_summary_source_text_field_name",
                    "interactive_content");
        });
    }

    // 测试用的账号：zhenju0111/13762692877/12345Qwert.
    @SFAAuditLog(bizName = "meeting_summary", entityClass = ActivityMessage.class, convertClass = ActivityMessageConverter.class)
    public void consume(ActivityMessage activityMessage) {
        if (!LicenseCheckUtil.checkAIExist(activityMessage.getTenantId())) {
            return;
        }
        String text = "";
        if (Strings.isNullOrEmpty(activityMessage.getObjectId())
                || Strings.isNullOrEmpty(activityMessage.getTenantId())) {
            return;
        }

        StopWatch stopWatch = new StopWatch("summaryConsume");
        ActivityMeetingSummaryContext allContentSummary = createAllContentSummary();
        ActivityMeetingSummaryContext chapterOverview = createChapterOverview();
        ActivityMeetingSummaryContext speakerSummary = createSpeakerSummary();

        stopWatch.lap("initContext");
        IObjectData activeRecordData = serviceFacade.findObjectData(
                User.systemUser(activityMessage.getTenantId()),
                activityMessage.getObjectId(),
                CommonConstant.ACTIVE_RECORD_API_NAME);
        stopWatch.lap("findObjectData");
        if (Objects.isNull(activeRecordData)) {
            return;
        }

        text = InheritRecordUtil.getStringValue(activeRecordData, SOURCE_TEXT_FIELD_NAME, "");
        if (text.length() <= 150) {
            log.warn("interactive_content is empty activityMessage={}", activityMessage);
            return;
        }
        boolean haveAllContentActivitySummary = haveAllContentActivitySummary(activeRecordData);
        // insertEmptyData(activityMessage); 格式化后的ActivitySummaryObj对象保存数据，弃用当前的保存方式
        stopWatch.lap("haveActivitySummary");
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        if (!haveAllContentActivitySummary) {
            parallelTask.submit(() -> execute(activityMessage, allContentSummary, activeRecordData));
        }
        parallelTask.submit(() -> execute(activityMessage, chapterOverview, activeRecordData));
        parallelTask.submit(() -> execute(activityMessage, speakerSummary,activeRecordData));

        try {
            parallelTask.await(600, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("ActivityMeetingSummaryService execute error e:", e);
        }
        stopWatch.lap("taskRun");

        if ("MailObj".equals(activityMessage.getSourceApiName())) {
            return;
        }
        if (!haveAllContentActivitySummary) {
            updateActiveRecordObj(activityMessage, allContentSummary, activeRecordData);
        }
        stopWatch.lap("updateActiveRecordObj");
        stopWatch.logSlow(30000);
    }

    public void executeByApiName(ActivityMessage activityMessage, String apiName) {
        ActivityMeetingSummaryContext context = null;
        IObjectData objectData = serviceFacade.findObjectData(
                User.systemUser(activityMessage.getTenantId()),
                activityMessage.getObjectId(),
                CommonConstant.ACTIVE_RECORD_API_NAME);
        switch (apiName) {
            case "prompt_sfa_speaker_summary":
                context = createSpeakerSummary();
                execute(activityMessage, context, objectData);
                break;
            case "prompt_sfa_chapter_overview":
                context = createChapterOverview();
                execute(activityMessage, context, objectData);
                break;
            case "prompt_sfa_all_content_summary":
                context = createAllContentSummary();
                execute(activityMessage, context, objectData);
                break;
            default:
                log.warn("ActivityMeetingSummaryService executeByApiName not found apiName={}", apiName);
                break;
        }
    }

    private String filterMultipleNewlines(String text) {
        if (Strings.isNullOrEmpty(text)) {
            return text;
        }
        // 过滤掉开头和结尾的特殊字符
        text = ActivityTodoService.removeSpecialCharacters(text,"'''");
        text = ActivityTodoService.removeSpecialCharacters(text,"```");
        // 将两个或更多连续的换行符替换为单个换行符
        return text.replaceAll("\\n{2,}", "\n").trim();
    }

    private void execute(ActivityMessage activityMessage, ActivityMeetingSummaryContext context, IObjectData activeRecordData) {
        StopWatch stopWatch = new StopWatch("execute");
        for (Map.Entry<String, String> item : context.getPromptApiNameMap().entrySet()) {
            String response = "";
            if (Boolean.TRUE.equals(context.getEnableInteractiveScenario())) {
                // 根据互动场景调用不同的提示词
                response = interactiveScenarioService.getAiResult(
                        InteractiveScenarioModel.ModelApiName.transition(item.getKey()),
                        new User(activityMessage.getTenantId(), activityMessage.getOpId()),
                        activeRecordData,
                        Maps.newHashMap());
            } else {
                // 直接调用
                response = AskForAIv2(activityMessage, item.getValue(), "");
            }
            stopWatch.lap("getAiResponse");
            if (Strings.isNullOrEmpty(response)) {
                continue;
            }
            response = filterMultipleNewlines(response);
            context.getAiResponseMap().put(item.getKey(), response);
        }
        updateDataToFormat(activityMessage, context);
        stopWatch.lap("updateDataToFormat");
        stopWatch.logSlow(10000);
    }

    private void updateActiveRecordObj(ActivityMessage activityMessage, ActivityMeetingSummaryContext context,
                                       IObjectData activeRecordData) {

        // 全文摘要
        String fullTextSummaryTitle = I18nClient.getInstance().getOrDefault(getI18nKey("full_text_summary"), 0,
                activityMessage.getLanguage(), "全文摘要");// ignoreI18n
        IObjectData objectData = new ObjectData();
        objectData.setId(activityMessage.getObjectId());
        objectData.setTenantId(activityMessage.getTenantId());
        String describeId = getDescribeId(activityMessage.getTenantId(), "ActiveRecordObj");
        objectData.setDescribeId(describeId);
        objectData.setDescribeApiName("ActiveRecordObj");
        objectData.set("interactive_processes", "4");
        String activeRecordContent = InheritRecordUtil.getStringValue(activeRecordData,
                CommonConstant.ACTIVE_RECORD_CONTENT, "");
        if (StringUtils.isBlank(activeRecordContent)) {
            emptyRecordContent(activityMessage, context, fullTextSummaryTitle, objectData);
        } else {
            additionRecordContent(activityMessage, context, fullTextSummaryTitle, objectData, activeRecordData);
        }
    }

    private void additionRecordContent(ActivityMessage activityMessage, ActivityMeetingSummaryContext context,
                                       String fullTextSummaryTitle, IObjectData objectData, IObjectData activeRecordData) {
        String contentE = activeRecordData.get(CommonConstant.ACTIVE_RECORD_CONTENT__E, String.class);
        String content = activeRecordData.get(CommonConstant.ACTIVE_RECORD_CONTENT, String.class);
        if (Strings.isNullOrEmpty(contentE)) {
            emptyRecordContent(activityMessage, context, fullTextSummaryTitle, objectData);
            return;
        }

        try {
            String textSummary = fullTextSummaryJsonToText(context.getAiResponseMap().get("full_text_summary"));
            if (Strings.isNullOrEmpty(textSummary)) {
                return;
            }

            Map<String, Object> contentEMap = JSON.parseObject(contentE, Map.class);
            Map<String, Object> xtMap = (Map<String, Object>) contentEMap.get("__xt");
            String tips = I18nClient.getInstance().getOrDefault(I18NKey.SFA_AI_CREATE_CONTENT, 0,
                        activityMessage.getLanguage(), "以下为纷享AI基于【互动内容】提取的互动纪要，供您参考！"); // ignoreI18n
            if(xtMap != null) {
                Map<String, Object> jsonMap = (Map<String, Object>) xtMap.get("__json");
                List<Object> contentList = (List<Object>) jsonMap.get("content");
                // 插入空行
                contentList.add(buildEmptyContent());
                contentList.add(buildContent(tips, null));
                contentList.add(buildEmptyContent());

                // 添加标题
                Map<String, Object> titleParagraph = buildContent(fullTextSummaryTitle, buildMarks("bold"));
                contentList.add(titleParagraph);

                // 添加内容
                Map<String, Object> contentParagraph = buildContent(textSummary, null);
                contentList.add(contentParagraph);
                // 更新__summeryNodeCount
                xtMap.put("__summeryNodeCount", (Integer) xtMap.getOrDefault("__summeryNodeCount", 0) + 5);
            }
            contentEMap.put("content", (content != null ? content : "") + tips + " " + fullTextSummaryTitle + " " + textSummary);
            objectData.set(CommonConstant.ACTIVE_RECORD_CONTENT, content + tips + " " + fullTextSummaryTitle + " " + textSummary);
            objectData.set(CommonConstant.ACTIVE_RECORD_CONTENT__E, JSON.toJSONString(contentEMap));

            log.info("updateActiveRecordObj before, activityMessage={} context={} objectData={}",
                    activityMessage,
                    context,
                    objectData);
            IObjectData result = serviceFacade.updateObjectData(User.systemUser(activityMessage.getTenantId()),
                    objectData);
        } catch (Exception e) {
            log.error("additionRecordContent parse contentE error activityMessage={} context={} contentE={}",
                    activityMessage, context, contentE, e);
        }
    }

    private void emptyRecordContent(ActivityMessage activityMessage, ActivityMeetingSummaryContext context,
                                    String fullTextSummaryTitle, IObjectData objectData) {
        try {
            String active_record_content = fullTextSummaryJsonToText(context.getAiResponseMap().get("full_text_summary"));
            List<Object> contentList = Lists.newArrayList();
            if (!Strings.isNullOrEmpty(active_record_content)) {
                contentList.add(buildContent(fullTextSummaryTitle, buildMarks("bold")));
                contentList.add(buildContent(active_record_content, null));
            }

            Map<String, Object> doc = Maps.newHashMap();
            doc.put(CRMFeedConstants.Field.TYPE, "doc");
            doc.put(CRMFeedConstants.Field.CONTENT, contentList);

            Map<String, Object> json = Maps.newHashMap();
            json.put(CRMFeedConstants.Field.__json, doc);

            Map<String, Object> map = Maps.newHashMap();
            map.put(CRMFeedConstants.Field.__xt, json);
            map.put("cmpt", "XT_TEXT");

            objectData.set("active_record_content",
                    Strings.isNullOrEmpty(active_record_content) ? "" : active_record_content);
            objectData.set("active_record_content__e", JSON.toJSONString(map));
            log.info("updateActiveRecordObj before, activityMessage={} context={} objectData={}",
                    activityMessage,
                    null,
                    objectData);
            User user = User.systemUser(activityMessage.getTenantId());
            IActionContext context1 = ActionContextExt.of(user, RequestContextManager.getContext())
                    .set("not_validate", true)
                    .getContext();
            serviceFacade.updateObjectData(user, objectData, context1);
        } catch (Exception e) {
            log.error("activityMessage={} context={}", activityMessage, context, e);
        }
    }

    private String getDescribeId(String tenantId, String name) {
        IObjectDescribe objectDescribe = serviceFacade.findObject(tenantId, name);
        if (Objects.isNull(objectDescribe)) {
            log.error("tenantId={} name={}", tenantId, name);
            return "";
        } else {
            return objectDescribe.getId();
        }
    }

    @Deprecated
    private void insertEmptyData(ActivityMessage activityMessage, ActivityMeetingSummaryContext context) {
        try {
            IObjectData objectData = new ObjectData();
            objectData.setCreatedBy("-10000");
            objectData.setOwner(Lists.newArrayList("-10000"));
            objectData.setRecordType(RECORD_TYPE_DEFAULT);
            objectData.set("active_record_id", activityMessage.getObjectId());
            objectData.setId(activityMessage.getObjectId());
            objectData.setTenantId(activityMessage.getTenantId());
            objectData.setDescribeId(context.getDescribeId());
            objectData.setDescribeApiName(context.getApiName());
            //
            log.info(
                    "ActivityMeetingSummaryService insertEmptyData before, activityMessage={} context={} objectData={}",
                    activityMessage,
                    context,
                    objectData);
            if (isExist(activityMessage, activityMessage.getObjectId())) {
                log.info("ActivityMeetingSummaryService message already exists={} context={}", activityMessage,
                        context);
                return;
            }
            serviceFacade.bulkSaveObjectData(Lists.newArrayList(objectData),
                    User.systemUser(activityMessage.getTenantId()));
        } catch (Exception e) {
            if (!Strings.isNullOrEmpty(e.getMessage()) && e.getMessage().contains("already exists")) {
                log.info("activityMessage={} context={}", activityMessage, context, e);
            } else {
                log.error("activityMessage={} context={}", activityMessage, context, e);
            }
        }
    }

    /**
     * 全文摘要json转换为纯文本
     * @param fullTextSummaryJson 全文摘要json
     * @return 纯文本
     */
    private String fullTextSummaryJsonToText(String fullTextSummaryJson) {
        if (ObjectUtils.isEmpty(fullTextSummaryJson)) {
            return "";
        }
        StringBuilder stringBuilder = new StringBuilder();
        JSONArray jsonArray = JSON.parseArray(fullTextSummaryJson);
        int index = 1;
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            if (jsonObject.getJSONArray("content").isEmpty()){
                continue;
            }
            stringBuilder.append(index);
            index++;
            stringBuilder.append(".");
            stringBuilder.append(jsonObject.getString("name"));
            stringBuilder.append("\n");
            jsonObject.getJSONArray("content").forEach(o -> {
                stringBuilder.append(" -");
                stringBuilder.append(o);
                stringBuilder.append("\n");
            });
            stringBuilder.append("\n");
        }
        return stringBuilder.toString();
    }

    /**
     * 保存模块结果为格式化的数据
     */
    private void updateDataToFormat(ActivityMessage activityMessage, ActivityMeetingSummaryContext context) {
        Map<String, String> aiResponseMap = context.getAiResponseMap();
        List<IObjectData> newObjectDataList = Lists.newArrayList();
        // 剩余的模块都只有一个结果 循环下次可以去掉
        for (String key : aiResponseMap.keySet()) {
            String jsonString = aiResponseMap.get(key);
            try {
                JSONArray jsonArray = JSON.parseArray(jsonString);
                int index = 1;
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    if (jsonObject.getJSONArray("content").isEmpty()){
                        continue;
                    }
                    IObjectData objectData = new ObjectData();
                    objectData.setCreatedBy("-10000");
                    objectData.setOwner(Lists.newArrayList(ObjectUtils.isEmpty(activityMessage.getOpId()) ? "1000" : activityMessage.getOpId()));
                    objectData.setTenantId(activityMessage.getTenantId());
                    objectData.setDescribeApiName("ActivityMeetingSummaryFormatObj");
                    objectData.set("active_record_id", activityMessage.getObjectId());
                    objectData.set("components_name", context.getFieldName());
                    objectData.set("display_order", index);
                    index++;
                    objectData.setName(jsonObject.getString("name"));
                    objectData.set("content", jsonObject.getJSONArray("content").toJSONString());
                    objectData.set("start_time", jsonObject.getString("start_time"));
//                    if ("speaker_summary".equals(key)){
//                        objectData.set("activity_user_id", "");
//                    }
                    newObjectDataList.add(objectData);
                }
            } catch (Exception e) {
                log.warn("jsonString={} key={} error", jsonString, key);
            }
        }
        // 插入数据
        if (newObjectDataList.isEmpty()) {
            return;
        }
        try {
            User user = User.systemUser(activityMessage.getTenantId());
            // 插入数据前先删除原来的数据
            SearchTemplateQuery query = new SearchTemplateQuery();
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, Tenantable.TENANT_ID, activityMessage.getTenantId());
            SearchUtil.fillFilterEq(filters, "is_deleted", 0);
            SearchUtil.fillFilterEq(filters, "active_record_id", activityMessage.getObjectId());
            SearchUtil.fillFilterEq(filters, "components_name", context.getFieldName());
            query.setFilters(filters);
            query.setLimit(100);
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, "ActivityMeetingSummaryFormatObj", query);
            if (!ObjectUtils.isEmpty(queryResult) && !ObjectUtils.isEmpty(queryResult.getData())) {
                serviceFacade.bulkDeleteDirect(queryResult.getData(), user);
            }
            // 保存新数据
            serviceFacade.bulkSaveObjectData(newObjectDataList, user);
        } catch (Exception e) {
            log.error("ins data error. activityMessage={} context={} newObjectDataList={}", activityMessage, context, newObjectDataList, e);
        }
    }

    @Deprecated
    private void updateData(ActivityMessage activityMessage, ActivityMeetingSummaryContext context) {
        try {
            IObjectData objectData = new ObjectData();
            objectData.setCreatedBy("-10000");
            objectData.setOwner(Lists.newArrayList("1000"));
            objectData.set("active_record_id", activityMessage.getObjectId());
            objectData.setId(activityMessage.getObjectId());
            objectData.setTenantId(activityMessage.getTenantId());
            objectData.setDescribeApiName(context.getApiName());
            List<Object> contentList = Lists.newArrayList();
            for (Map.Entry<String, String> item : context.getAiResponseMap().entrySet()) {
                if (!Strings.isNullOrEmpty(item.getValue())) {
                    String itemKeyTitle = I18nClient.getInstance().getOrDefault(getI18nKey(item.getKey()), 0,
                            activityMessage.getLanguage(), "摘要");// ignoreI18n
                    contentList.add(buildContent(itemKeyTitle, buildMarks("bold")));
                    contentList.add(buildContent(item.getValue(), null));
                }
            }

            Map<String, Object> doc = Maps.newHashMap();
            doc.put(CRMFeedConstants.Field.TYPE, "doc");
            doc.put(CRMFeedConstants.Field.CONTENT, contentList);

            Map<String, Object> json = Maps.newHashMap();
            json.put(CRMFeedConstants.Field.__json, doc);

            Map<String, Object> map = Maps.newHashMap();
            map.put(CRMFeedConstants.Field.__xt, json);
            map.put("cmpt", "XT_TEXT");
            // 这个text如果不传递，专表中就是空的，但是什么都不影响，因为都是根据extra去渲染的
            // map.put(CRMFeedConstants.Field.TEXT, allText);
            objectData.set(context.getFieldName(), map);
            log.info("updateData before,id:{} objectData={}", activityMessage.getObjectId(), objectData.getId());

            IActionContext actionContext = new ActionContext();
            actionContext.setEnterpriseId(activityMessage.getTenantId());
            actionContext.setUserId("-10000");

            objectDataService.batchUpdateIgnoreOther(Lists.newArrayList(objectData),
                    Lists.newArrayList(context.getFieldName()), actionContext);
        } catch (Exception e) {
            log.error("activityMessage={} context={}", activityMessage, context, e);
        }
    }

    private List<Object> buildMarks(String typeValue) {
        if (Strings.isNullOrEmpty(typeValue)) {
            return null;
        }

        Map<String, Object> type = new HashMap<>();
        type.put("type", typeValue);

        List<Object> marks = Lists.newArrayList();
        marks.add(type);
        return marks;
    }

    private Map<String, Object> buildContent(String text, List<Object> marks) {
        Map<String, Object> contentMap = new HashMap<>();
        contentMap.put("type", "text");
        contentMap.put("text", removeMarkdownSymbols(text));

        if (!CollectionUtils.isEmpty(marks)) {
            contentMap.put("marks", marks);
        }

        List<Object> contentList = Lists.newArrayList();
        contentList.add(contentMap);

        Map<String, Object> attrs = new HashMap<>();
        attrs.put("textAlign", "left");
        attrs.put("isSFAActivityCreated", "true");

        Map<String, Object> paragraph = new HashMap<>();
        paragraph.put("type", "paragraph");
        paragraph.put("attrs", attrs);
        paragraph.put("content", contentList);
        return paragraph;
    }

    private Map<String, Object> buildEmptyContent() {
        Map<String, Object> attrs = new HashMap<>();
        attrs.put("textAlign", "left");
        Map<String, Object> paragraph = new HashMap<>();
        paragraph.put("type", "paragraph");
        paragraph.put("attrs", attrs);
        attrs.put("isSFAActivityCreated", "true");
        return paragraph;
    }


    public String AskForAIv2(ActivityMessage activityMessage, String apiName, String text) {
        try {
            AiRestProxyModel.Arg aiArg = AiRestProxyModel.Arg.builder()
                    .apiName(apiName)
                    .bingObjectDataId(activityMessage.getObjectId())
                    .sceneVariables(Maps.newHashMap())
                    .build();
            return completions.requestCompletion(new User(activityMessage.getTenantId(), activityMessage.getOpId()), aiArg);
        } catch (Exception e) {
            log.error("ActivityMeetingSummaryService AskForAIv2 error activityMessage={} apiName={}", activityMessage,
                    apiName, e);
            return "";
        }
    }

    public boolean isExist(ActivityMessage message, String objectId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        query.setOffset(0);
        query.setPermissionType(0);
        query.setNeedReturnQuote(Boolean.FALSE);
        query.setNeedReturnCountNum(Boolean.FALSE);
        List<IFilter> filters = Lists.newArrayList();
        IFilter filter = new Filter();
        filter.setFieldName(IObjectData.ID);
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(objectId));
        filters.add(filter);
        query.setFilters(filters);

        IActionContext context = new ActionContext();
        context.setEnterpriseId(message.getTenantId());
        context.setUserId("-10000");
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(
                new User(message.getTenantId(), "-10000"), "ActivityMeetingSummaryObj", query,
                Lists.newArrayList(IObjectData.ID));
        return queryResult != null && !CollectionUtils.isEmpty(queryResult.getData());
    }

    public String getSpeakerContent(ActivityMessage activityMessage) {
        
        List<InteractiveDocument> interactiveDocuments = activityMongoDao.queryListByActiveRecordId(activityMessage.getTenantId(), 
                                                                                                activityMessage.getObjectId(), 
                                                                                                0, 500, true);
        if (CollectionUtils.isEmpty(interactiveDocuments)) {
            return "";
        }

        String text = "";
        List<IObjectData> activityUsers = Lists.newArrayList();

        try {
            activityUsers = activityUserService.getActivityUsers(activityMessage.getTenantId(), activityMessage.getObjectId());
        } catch (Exception e) {
            log.warn("ActivityMeetingSummaryService getActivityUsers error activityMessage={}", activityMessage, e);
        }
        if (CollectionUtils.isEmpty(activityUsers)) {
            text = interactiveDocuments.stream()
            .map(x -> {
                String userName = x.getUserName();
                if (userName.startsWith("user_")) {
                    userName = "发言人" + userName.substring(5);// ignoreI18n
                }
                return userName + "：" + x.getContent();
            })
            .collect(Collectors.joining("\n"));
        } else {
            Map<String, String> idToNameMap = new HashMap<>();
            for (IObjectData activityUser : activityUsers) {
                idToNameMap.put(activityUser.getId(), activityUser.get("user_name", String.class));
            }
            for (InteractiveDocument document : interactiveDocuments) {
                String userName = idToNameMap.get(document.getActivityUserId());
                if (userName.startsWith("user_")) {
                    userName = "发言人" + userName.substring(5);// ignoreI18n
                }
                text += userName + "：" + document.getContent() + "\n";
            }
        }
        return text;
    }

    private String outputShortString(String text) {
        if (Strings.isNullOrEmpty(text)) {
            return "";
        }

        return text.length() > 20 ? text.substring(0, 20) : text;
    }

    private String removeMarkdownSymbols(String text) {
        if (Strings.isNullOrEmpty(text)) {
            return "";
        }
        // 移除所有的 #、* 符号和多余的空行
        return text.replaceAll("#+|\\*+", "") // 移除一个或多个#号和*号
                .trim(); // 移除首尾空白
    }

    public String getI18nKey(String code) {
        return "sfa.activity.meeting.summary." + code;
    }

//    // 添加工厂方法
//    private ActivityMeetingSummaryContext createActiveRecordContent() {
//        ActivityMeetingSummaryContext context = ActivityMeetingSummaryContext.builder()
//                .title("业务摘要")// ignoreI18n
//                .fieldName("active_record_content")
//                .apiName("ActivityMeetingSummaryObj")
//                .promptApiNameMap(Maps.newHashMap())
//                .aiResponseMap(Maps.newHashMap())
//                .build();
//        context.getPromptApiNameMap().put("topic_summary", "prompt_sfa_topic_summary");
//        context.getPromptApiNameMap().put("customer_info_summary", "prompt_sfa_customer_info_summary");
//        context.getPromptApiNameMap().put("opportunity_summary", "prompt_sfa_opportunity_summary");
//        context.getPromptApiNameMap().put("competitor_info_summary", "prompt_sfa_competitor_info_summary");
//        context.getPromptApiNameMap().put("participant_analysis", "prompt_sfa_participant_analysis");
//        return context;
//    }

    /** 全文摘要 */
    private ActivityMeetingSummaryContext createAllContentSummary() {
        ActivityMeetingSummaryContext context = ActivityMeetingSummaryContext.builder()
                .title("full_text_summary")
                .fieldName("all_content_summary")
                .apiName("ActivityMeetingSummaryObj")
                .promptApiNameMap(Maps.newHashMap())
                .aiResponseMap(Maps.newHashMap())
                .enableInteractiveScenario(true)
                .build();
        context.getPromptApiNameMap().put("full_text_summary", "prompt_sfa_full_text_summary");
        return context;
    }

    /** 章节概览 */
    private ActivityMeetingSummaryContext createChapterOverview() {
        ActivityMeetingSummaryContext context = ActivityMeetingSummaryContext.builder()
                .title("chapter_preview")
                .fieldName("chapter_overview")
                .apiName("ActivityMeetingSummaryObj")
                .promptApiNameMap(Maps.newHashMap())
                .aiResponseMap(Maps.newHashMap())
                .enableInteractiveScenario(true)
                .build();
        context.getPromptApiNameMap().put("chapter_preview", "prompt_sfa_chapter_preview");
        return context;
    }

//    // 重要议题
//    private ActivityMeetingSummaryContext createImportantIssue() {
//        ActivityMeetingSummaryContext context = ActivityMeetingSummaryContext.builder()
//                .title("important_issue")
//                .fieldName("important_issue")
//                .apiName("ActivityMeetingSummaryObj")
//                .promptApiNameMap(Maps.newHashMap())
//                .aiResponseMap(Maps.newHashMap())
//                .build();
//        context.getPromptApiNameMap().put("important_issue", "prompt_sfa_important_issue");
//        return context;
//    }

    /** 发言人总结 */
    private ActivityMeetingSummaryContext createSpeakerSummary() {
        ActivityMeetingSummaryContext context = ActivityMeetingSummaryContext.builder()
                .title("speaker_summary")
                .fieldName("speaker_summary")
                .apiName("ActivityMeetingSummaryObj")
                .promptApiNameMap(Maps.newHashMap())
                .aiResponseMap(Maps.newHashMap())
                .enableInteractiveScenario(false)
                .build();
        context.getPromptApiNameMap().put("speaker_summary", "prompt_sfa_speaker_summary");
        return context;
    }

    /**
     * 检查 ActiveRecordObj 的 active_record_content__e 字段是否包含由AI生成的摘要标记。
     * @param activeRecordData 活动记录对象数据
     * @return 如果包含AI生成的摘要标记，则返回 true，否则返回 false。
     */
    private boolean haveAllContentActivitySummary(IObjectData activeRecordData) {
        String contentE = activeRecordData.get(CommonConstant.ACTIVE_RECORD_CONTENT__E, String.class);
        if (Strings.isNullOrEmpty(contentE)) {
            return false;
        }

        try {
            Map<String, Object> contentEMap = JSON.parseObject(contentE, Map.class);
            Object xtObject = contentEMap.get("__xt");
            if (xtObject == null || !(xtObject instanceof Map)) {
                return false;
            }
            Map<String, Object> xtMap = (Map<String, Object>) xtObject;

            Object jsonObject = xtMap.get("__json");
            if (jsonObject == null || !(jsonObject instanceof Map)) {
                return false;
            }
            Map<String, Object> jsonMap = (Map<String, Object>) jsonObject;

            Object contentObject = jsonMap.get("content");
            if (contentObject == null || !(contentObject instanceof List)) {
                return false;
            }
            List<Object> contentList = (List<Object>) contentObject;

            for (Object paragraphObject : contentList) {
                if (paragraphObject == null || !(paragraphObject instanceof Map)) {
                    continue;
                }
                Map<String, Object> paragraphMap = (Map<String, Object>) paragraphObject;
                Object attrsObject = paragraphMap.get("attrs");
                if (attrsObject instanceof Map) {
                    Map<String, Object> attrsMap = (Map<String, Object>) attrsObject;
                    if (attrsMap.containsKey("isSFAActivityCreated")) {
                        // 只要存在 isSFAActivityCreated 属性即可，无需判断其值
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            log.error("Failed to parse active_record_content__e for checking AI summary existence. activeRecordDataId={}, contentE={}",
                      activeRecordData.getId(), contentE, e);
            return false; // 解析失败或结构不符时，认为不存在
        }

        return false; // 遍历完成未找到标记
    }
}
