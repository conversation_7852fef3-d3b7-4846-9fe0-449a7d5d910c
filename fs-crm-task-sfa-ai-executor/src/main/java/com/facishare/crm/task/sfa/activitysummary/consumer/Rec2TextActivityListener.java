package com.facishare.crm.task.sfa.activitysummary.consumer;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.activitysummary.service.ActivityTodoService;
import com.facishare.crm.task.sfa.activitysummary.service.AttachmentStrategy;
import com.facishare.crm.task.sfa.activitysummary.service.RecordingTaskService;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/12/10 20:30
 * @description:
 */
@Slf4j
@Component
public class Rec2TextActivityListener extends AbstractActivityCommonListener {

    @Resource
    private ActivityTodoService activityTodoService;
    @Resource
    private AttachmentStrategy attachmentStrategy;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private RecordingTaskService recordingTaskService;

    @Override
    String getSection() {
        return "sfa-ai-activity-text-consumer";
    }


    @Override
    void consume(ActivityMessage activityMessage) {

        log.info("Rec2TextActivityListener consume activityMessage:{}", activityMessage);
        String stage = activityMessage.getStage();
        if (ObjectUtils.isEmpty(stage)) {
            return;
        }
        switch (stage) {
            case "AddNoAttachment":  // 无附件新增
                handleActivityTodo(activityMessage, true);
                break;
            case "Add":
                attachmentStrategy.handleFile(activityMessage);
                break;
            case "file2text":  // 附件转文本
                handleActivityTodo(activityMessage, true);
                recordingTaskService.updateResourceUsage(activityMessage.getTenantId(), activityMessage.getOpId(), activityMessage.getObjectId(), Utils.ACTIVE_RECORD_API_NAME);
                break;
            case "realtime2text":  // 实时音频转文字
                break;
            case "realtime2textDone":// 实时音频转文字(全部结束时)
                handleActivityTodo(activityMessage, false);
                updateInteractiveProcesses(activityMessage);
                break;
        }
    }

    @Override
    public void init() {
        super.init();
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        super.onApplicationEvent(event);
    }

    /**
     * 待办-已做异步处理
     * @param skipMongo 是否跳过从mongo获取数据
     */
    public void handleActivityTodo(ActivityMessage activityMessage,boolean skipMongo){
        try {
            log.info("activityTodo auto.");
            activityTodoService.execute(activityMessage, true, skipMongo);
        }catch (Exception e){
            log.error("Rec2TextActivityListener activityTodoService e:",e);
        }
    }

    private void updateInteractiveProcesses(ActivityMessage activityMessage) {
        try {
            User user = User.systemUser(activityMessage.getTenantId());
            List<IFilter> filters = new ArrayList<>();
            SearchUtil.fillFilterEq(filters, DBRecord.ID, activityMessage.getObjectId());
            SearchUtil.fillFilterNotIN(filters, "interactive_processes", Lists.newArrayList("2", "4"));
            SearchTemplateQuery query = new SearchTemplateQuery();
            query.setFilters(filters);
            query.setLimit(1);
            query.setPermissionType(0);
            List<IObjectData> dataList = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, Utils.ACTIVE_RECORD_API_NAME, query, Lists.newArrayList(DBRecord.ID, Tenantable.TENANT_ID, IObjectData.DESCRIBE_API_NAME, "interactive_processes")).getData();
            if (dataList.isEmpty()) {
                return;
            }
            IObjectData data = dataList.get(0);
            data.set("interactive_processes", "2");
            serviceFacade.batchUpdateByFields(user, Collections.singletonList(data), Collections.singletonList("interactive_processes"));
        } catch (Exception e) {
            log.error("updateInteractiveProcesses error", e);
        }
    }
}
