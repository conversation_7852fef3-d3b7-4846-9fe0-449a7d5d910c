package com.facishare.crm.task.sfa.activitysummary.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import com.facishare.paas.appframework.core.model.User;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class FixJSONFormatService {
    private static final String FIX_PROMPT_API_NAME = "prompt_fix_invalid_json_by_format";
    @Autowired
    private CompletionsService completionsService;

    public <T> List<T> getDataListFixedInvalidJSON(User user, String jsonFormat, String jsonData, Class<T> tClass, boolean retainNewline) {
        if (StringUtils.isBlank(jsonData)) {
            return Collections.emptyList();
        }
        return parseJsonArray(user, jsonFormat, jsonData, tClass, retainNewline);
    }

    public <T> T getDataFixedInvalidJSON(User user, String jsonFormat, String jsonData, Class<T> tClass) {
        if (StringUtils.isBlank(jsonData)) {
            return null;
        }

        try {
            String cleaned = cleanJSONStr(jsonData);
            return JSON.parseObject(cleaned, tClass);
        } catch (Exception e) {
            log.warn("JSON解析失败，尝试AI修复: {}", jsonData, e);
            try {
                String fixedJson = chatCompleteNoPrompt(user, jsonFormat, jsonData);
                String cleaned = cleanJSONStr(fixedJson);
                return StringUtils.isBlank(cleaned) ?
                        null :
                        JSON.parseObject(cleaned, tClass);
            } catch (Exception exception) {
                log.error("AI修复失败", exception);
                return null;
            }
        }
    }

    @NotNull
    private String cleanJSONStr(String jsonData) {
        jsonData = truncateMarkdownJSON(jsonData);
        String cleaned = removeMarkdown(jsonData);
        cleaned = removeEscapeCharacters(cleaned);
        return cleaned;
    }

    private <T> List<T> parseJsonArray(User user, String jsonFormat, String jsonData, Class<T> tClass, boolean retainNewline) {
        try {
            String cleanedJson = fixJSON(jsonData, retainNewline);
            return JSON.parseArray(cleanedJson, tClass);
        } catch (JSONException e) {
            log.warn("JSON解析失败，尝试AI修复: {}", jsonData, e);
            try {
                String fixedJson = chatCompleteNoPrompt(user, jsonFormat, jsonData);
                String cleaned = cleanJSONStr(fixedJson);
                return StringUtils.isBlank(cleaned) ?
                        Collections.emptyList() :
                        JSON.parseArray(cleaned, tClass);
            } catch (Exception exception) {
                log.error("AI修复失败", exception);
                return Collections.emptyList();
            }
        }
    }

    public String fixJSON(String jsonData, boolean retainNewline) {
        jsonData = truncateMarkdownJSON(jsonData);
        String cleaned = removeMarkdown(jsonData);
        if (!retainNewline) {
            cleaned = removeEscapeCharacters(cleaned);
        }

        if (cleaned.contains("[") && cleaned.contains("]")) {
            return extractJsonArray(cleaned);
        }
        if (cleaned.contains("{") && cleaned.contains("}")) {
            return wrapObjectInArray(cleaned);
        }
        return "[]";
    }

    private String truncateMarkdownJSON(String json) {
        int startIndex = json.indexOf("```json");
        if (startIndex == -1) {
            return json;
        }
        startIndex += "```json".length();
        int endIndex = json.indexOf("```", startIndex);
        if (endIndex == -1) {
            return json;
        }
        return json.substring(startIndex, endIndex).trim();
    }

    private String removeMarkdown(String json) {
        return json.replaceAll("```json", "")
                .replaceAll("```", "")
                .replace("***", "")
                .trim();
    }

    private String removeEscapeCharacters(String json) {
        return json.replaceAll("\\\\\\\\n", "")
                .replaceAll("\\\\n", "")
                .replaceAll("\\\\", "");
    }

    private String extractJsonArray(String json) {
        int start = json.indexOf("[");
        int end = json.lastIndexOf("]") + 1;
        return json.substring(start, end);
    }

    private String wrapObjectInArray(String json) {
        return "[" + json.replaceAll("^\"|\"$", "") + "]";
    }

    private String chatCompleteNoPrompt(User user, String jsonFormat, String jsonData) {
        Map<String, Object> sceneVariables = Maps.newHashMap();
        sceneVariables.put("invalidJson", jsonData);
        sceneVariables.put("jsonFormat", jsonFormat);
        AiRestProxyModel.Arg arg = AiRestProxyModel.Arg.builder()
                .apiName(FIX_PROMPT_API_NAME)
                .sceneVariables(sceneVariables)
                .build();
        return completionsService.requestCompletion(user, arg);
    }

}
