package com.facishare.crm.task.sfa.activitysummary.service.attendees.insight;

import com.alibaba.fastjson2.JSON;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.activity.mongo.ActivityMongoDao;
import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.crm.task.sfa.activitysummary.model.ActivityContactInsightModel;
import com.facishare.crm.task.sfa.activitysummary.model.AttendeesInsightModel;
import com.facishare.crm.task.sfa.activitysummary.model.AttendeesInsightModel.AttendeesInsightMessage;
import com.facishare.crm.task.sfa.activitysummary.service.ActivityContactInsightService;
import com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.handler.InsightHandlerManager;
import com.facishare.crm.task.sfa.bizfeature.model.FeatureMqModel;
import com.facishare.crm.task.sfa.bizfeature.service.FeatureValueProducer;
import com.facishare.crm.task.sfa.common.constants.CommonConstant;
import com.facishare.crm.task.sfa.common.gray.GrayUtils;
import com.facishare.crm.task.sfa.common.util.Safes;
import com.facishare.crm.task.sfa.util.OriginCorpusUtils;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.fxiaoke.rocketmq.producer.DefaultTopicMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;


@Slf4j
@Service
public class AttendeesInsightService {



    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private ActivityMongoDao activityMongoDao;
    @Autowired
    private InsightHandlerManager insightHandlerManager;
    @Resource(name = "attendeeInsightsProducer")
    private AutoConfMQProducer attendeeInsightsProducer;
    @Autowired
    private FeatureValueProducer featureValueProducer;
    @Autowired
    private ActivityContactInsightService activityContactInsightService;


    public void insight(AttendeesInsightMessage attendeesInsightMessage) {
        String activeRecordId = attendeesInsightMessage.getActiveRecordId();
        String tenantId = attendeesInsightMessage.getTenantId();

        if (GrayUtils.skipAttendeesInsight(tenantId)) {
            return;
        }

        List<InteractiveDocument> documents = activityMongoDao.queryListByActiveRecordId(tenantId, activeRecordId, 0, 500, true);
        List<IObjectData> activityUserList = queryActivityUserList(tenantId, activeRecordId);
        Optional<IObjectData> unboundUser = activityUserList.stream().filter(d -> Safes.isEmpty(d.get("user_api_name", String.class))).findAny();
        if (unboundUser.isPresent()) {
            log.warn("Unbound user found in activity user: {}", unboundUser.get().getId());
            return;
        }
        IObjectData activeRecord = serviceFacade.findObjectData(User.systemUser(tenantId), activeRecordId, Utils.ACTIVE_RECORD_API_NAME);

        Map<String, String> userIdNameMap = getUserIdNameMap(activityUserList);
        String corpusOriginText = OriginCorpusUtils.makeUpCorpusOriginText(documents, activityUserList);
        List<IObjectData> questionList = queryQuestionList(tenantId, activeRecordId);

        AttendeesInsightModel.AttendeesInsightExtendData extendData = new AttendeesInsightModel.AttendeesInsightExtendData();
        extendData.setCorpusOriginText(corpusOriginText);
        extendData.setQuestionList(questionList);
        extendData.setActivityUserList(activityUserList);
        extendData.setDocuments(documents);
        extendData.setUserIdNameMap(userIdNameMap);
        extendData.setActiveRecord(activeRecord);
        attendeesInsightMessage.setExtendData(extendData);
        insightHandlerManager.executeInsight(attendeesInsightMessage);

        featureValueProducer.sendParticipantMessage(FeatureMqModel.Message.builder()
                .tenantId(tenantId)
                .userId(User.SUPPER_ADMIN_USER_ID)
                .objectId(activeRecordId)
                .objectApiName(Utils.ACTIVE_RECORD_API_NAME)
                .build());
        ActivityContactInsightModel.Arg arg = new ActivityContactInsightModel.Arg();
        arg.setTenantId(tenantId);
        arg.setActiveRecordId(activeRecordId);
        activityContactInsightService.execute(arg);
    }


    public List<IObjectData> queryQuestionList(String tenantId, String activeRecordId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterIn(query.getFilters(), "active_record_id", activeRecordId);
        SearchUtil.fillFilterEq(query.getFilters(), "question_type", "1");
        SearchUtil.fillFilterEq(query.getFilters(), IObjectData.IS_DELETED, 0);
        query.setLimit(AppFrameworkConfig.getMaxQueryLimit());
        return serviceFacade.findBySearchQuery(User.systemUser(tenantId), CommonConstant.ACTIVITY_QUESTION_API_NAME, query).getData();
    }

    public List<IObjectData> queryActivityUserList(String tenantId, String activeRecordId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterEq(query.getFilters(), "active_record_id", activeRecordId);
        SearchUtil.fillFilterEq(query.getFilters(), IObjectData.IS_DELETED, 0);
        query.setLimit(AppFrameworkConfig.getMaxQueryLimit());
        return serviceFacade.findBySearchQuery(User.systemUser(tenantId), "ActivityUserObj", query).getData();
    }

    private Map<String, String> getUserIdNameMap(List<IObjectData> activityUserList) {
        return activityUserList.stream().collect(Collectors.toMap(
                IObjectData::getId,d -> d.get("user_name", String.class)
        ));
    }

    public void sendInsightMessage(String tenantId, String activeRecordId, List<String> insightTypeList) {
        try {
            IObjectData activeRecord = serviceFacade.findObjectData(User.systemUser(tenantId), activeRecordId, Utils.ACTIVE_RECORD_API_NAME);
            String type = activeRecord.get("interactive_render_types", String.class);
            String status = activeRecord.get("interactive_processes", String.class);
            if (StringUtils.equalsAny(type, "corpus_audio", "corpus_video") && StringUtils.equals(status, "1")) {
                log.warn("未完成的录音/腾讯会议场景: {}", activeRecordId);
                return;
            }
            AttendeesInsightMessage message = new AttendeesInsightMessage();
            message.setTenantId(tenantId);
            message.setActiveRecordId(activeRecordId);
            message.setInsightTypeList(insightTypeList);
            DefaultTopicMessage topicMessage = new DefaultTopicMessage(JSON.toJSONBytes(message));
            attendeeInsightsProducer.send(topicMessage);
            log.warn("Send attendees insight message: {}", JSON.toJSONString(message));
        } catch (Exception e) {
            log.error("Send attendees insight message error", e);
        }
    }
}
