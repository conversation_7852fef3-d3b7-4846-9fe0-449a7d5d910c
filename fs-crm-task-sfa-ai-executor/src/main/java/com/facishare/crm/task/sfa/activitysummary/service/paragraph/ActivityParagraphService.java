package com.facishare.crm.task.sfa.activitysummary.service.paragraph;

import com.facishare.crm.sfa.audit.log.SFAAuditLog;
import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.activitysummary.model.ParagraphContext;
import com.facishare.crm.task.sfa.activitysummary.model.auditlog.ActivityMessageConverter;
import com.facishare.crm.task.sfa.activitysummary.service.ActiveRecordDataService;
import com.facishare.crm.task.sfa.activitysummary.service.strategy.ActivityProcessStrategyService;
import com.facishare.crm.task.sfa.common.constants.CommonConstant;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.metadata.cache.RedissonServiceImpl;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;

@Service
@Slf4j
public class ActivityParagraphService {
    @Autowired
    private DocumentProcessService documentProcessService;

    @Autowired
    private ContentBatchProcessService contentBatchProcessService;

    @Autowired
    private TextSegmentationService textSegmentationService;

    @Autowired
    private ActiveRecordDataService activeRecordDataService;

    @Autowired
    private RedissonServiceImpl redissonService;

    @Autowired
    private ActivityProcessStrategyService activityProcessStrategyService;

    private static final int MAX_LOOP_COUNT = 1000; // 最大循环次数，防止死循环

    /**
     * 查询活动记录数据
     *
     * @param user     用户
     * @param objectId 对象ID
     * @return 活动记录数据
     */
    public IObjectData queryActiveRecord(User user, String objectId) {
        IObjectData activeRecordData = activeRecordDataService.queryActiveRecordDataWithFields(user, objectId,
                Lists.newArrayList(
                        DBRecord.ID,
                        IObjectData.NAME,
                        CommonConstant.INTERACTIVE_CONTENT,
                        CommonConstant.NEW_OPPORTUNITY_ID,
                        CommonConstant.ACCOUNT_ID,
                        CommonConstant.LEADS_ID,
                        IObjectData.DESCRIBE_API_NAME));
        if (activeRecordData == null) {
            log.error("查询活动记录数据为空，objectId: {}", objectId);
            return null;
        }
        return activeRecordData;
    }

    /**
     * 获取交互内容并验证其有效性
     *
     * @param activeRecordData 活动记录数据
     * @param objectId 对象ID，用于日志记录
     * @return 有效的交互内容，如果内容无效则返回null
     */
    public String getInteractiveContent(IObjectData activeRecordData, String objectId) {
        String interactiveContent = activeRecordData.get(CommonConstant.INTERACTIVE_CONTENT, String.class);
        if (StringUtils.isBlank(interactiveContent) || interactiveContent.length() < 150) {
            log.warn("活动记录数据中没有交互内容，或少于150个字，objectId: {}", objectId);
            return null;
        }
        return interactiveContent;
    }

    @SFAAuditLog(bizName = "activity_paragraph", entityClass = ActivityMessage.class, convertClass = ActivityMessageConverter.class)
    public void consumerFileToText(ActivityMessage activityMessage) {
        String tenantId = activityMessage.getTenantId();
        User user = User.systemUser(tenantId);
        lock(user, activityMessage, activityProcessStrategyService::processFileToText);
    }

    @SFAAuditLog(bizName = "activity_paragraph", entityClass = ActivityMessage.class, convertClass = ActivityMessageConverter.class)
    public void consumerRealtimeToTextDone(ActivityMessage activityMessage) {
        String tenantId = activityMessage.getTenantId();
        User user = User.systemUser(tenantId);
        lock(user, activityMessage, activityProcessStrategyService::processRealtimeToTextDone);
    }

    public void lock(User user, ActivityMessage activityMessage, BiConsumer<User, ActivityMessage> consumer) {
        String objectId = activityMessage.getObjectId();
        String lockKey = user.getTenantId() + "_" + objectId + "_paragraph";
        RLock lock = redissonService.tryLock(0, 10, TimeUnit.MINUTES, lockKey);
        if (lock == null) {
            log.warn("活动段落处理已在进行中，跳过此次处理, objectId: {}", objectId);
            return;
        }
        try {
            if (documentProcessService.hasParagraphByObjectId(user.getTenantId(), objectId)) {
                log.info("已存在段落记录，跳过处理 objectId: {}", objectId);
                return;
            }
            consumer.accept(user, activityMessage);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 分批处理文档并进行分段总结
     *
     * @param user     用户
     * @param objectId 对象ID
     */
    public void processTextWithSegmentation(User user, String objectId) {
        IObjectData activeRecordData = queryActiveRecord(user, objectId);
        if (activeRecordData == null) {
            return;
        }

        String interactiveContent = getInteractiveContent(activeRecordData, objectId);
        if (interactiveContent == null) {
            return;
        }

        textSegmentationService.processLongText(user, interactiveContent, activeRecordData);
    }

    /**
     * 分批处理文档并进行分段总结
     */
    public void processDocumentsWithSegmentation(User user, String objectId) {
        int pageNo = 0;
        String previousSummary = null;
        ParagraphContext paragraphContext = new ParagraphContext();
        IObjectData activeRecordData = queryActiveRecord(user, objectId);
        if (activeRecordData == null) {
            return;
        }

        String interactiveContent = getInteractiveContent(activeRecordData, objectId);
        if (interactiveContent == null) {
            return;
        }

        paragraphContext.setActiveRecordData(activeRecordData);
        paragraphContext.setType(ParagraphContext.MONGO_KEY);
        for (int loopCount = 1; loopCount <= MAX_LOOP_COUNT; loopCount++) {
            StopWatch stopWatch = StopWatch.create("ProcessDocumentsWithSegmentation");
            try {
                List<InteractiveDocument> pageDocuments = documentProcessService.queryDocumentsPage(user.getTenantId(),
                        objectId, pageNo);
                stopWatch.lap("查询文档-第" + pageNo + "页");

                // 检查是否是最后一批数据
                boolean isLastBatch = CollectionUtils.isEmpty(pageDocuments);

                // 如果是最后一批且没有内容，在循环内标记isEnd
                if (isLastBatch && paragraphContext.isContentEmpty()) {
                    paragraphContext.setEnd(true);
                }
                previousSummary = contentBatchProcessService.processPageDocuments(user, pageDocuments, previousSummary,
                        paragraphContext, stopWatch);
                stopWatch.lap("processPageDocuments");

                // 如果是最后一批且没有更多数据，跳出循环
                if (isLastBatch) {
                    break;
                }
            } catch (Exception e) {
                log.error("处理第{}次循环时发生异常", loopCount, e);
            } finally {
                pageNo++;
            }
            stopWatch.logSlow(1000);
        }

        contentBatchProcessService.processFinalContent(user, previousSummary, paragraphContext);
    }
}
