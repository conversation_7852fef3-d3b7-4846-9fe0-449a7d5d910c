package com.facishare.crm.task.sfa.activitysummary.service;

import com.facishare.ai.api.dto.PromptCompletions;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.activitysummary.enums.FileTypeEnum;
import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import com.facishare.crm.task.sfa.rest.AiRestProxy;
import com.facishare.crm.task.sfa.util.ActivityUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 图片附件处理类
 * 支持多种图片格式的解析，包括：
 * - 常见格式：PNG、JPG、JPEG、GIF、WEBP、BMP
 * - TIFF格式：TIFF、TIF
 * - 图标格式：ICO、DIB、ICNS
 * - 特殊格式：SGI、APNG
 * - JPEG2000系列：J2C、J2K、JP2、JPC、JPF、JPX
 * - 新一代格式：HEIC、HEIF
 *
 * <AUTHOR> Assistant
 * @date 2024/12/19
 */
@Component
@Slf4j
public class ImageAttachment extends AbstractFile2Text implements File2Text {

    @Autowired
    private AiRestProxy aiRestProxy;

    @Override
    public boolean support(FileTypeEnum fileType) {
        return fileType == FileTypeEnum.PNG ||
                fileType == FileTypeEnum.JPG ||
                fileType == FileTypeEnum.JPEG ||
                fileType == FileTypeEnum.GIF ||
                fileType == FileTypeEnum.WEBP ||
                fileType == FileTypeEnum.BMP ||
                fileType == FileTypeEnum.TIFF ||
                fileType == FileTypeEnum.TIF ||
                fileType == FileTypeEnum.ICO ||
                fileType == FileTypeEnum.DIB ||
                fileType == FileTypeEnum.ICNS ||
                fileType == FileTypeEnum.SGI ||
                fileType == FileTypeEnum.J2C ||
                fileType == FileTypeEnum.J2K ||
                fileType == FileTypeEnum.JP2 ||
                fileType == FileTypeEnum.JPC ||
                fileType == FileTypeEnum.JPF ||
                fileType == FileTypeEnum.JPX ||
                fileType == FileTypeEnum.HEIC ||
                fileType == FileTypeEnum.HEIF ||
                fileType == FileTypeEnum.APNG;
    }

    @Override
    public void execute(ActivityMessage message, IObjectData activityData) {
        try {
            String imageText = parseImageText(message);
            rec2TextService.updateActivityText(message.getTenantId(), message.getObjectId(), message.getObjectApiName(), imageText);
            sendActivityMessage(message);
        } catch (Exception e) {
            log.error("Error processing image attachment for message: {}", message, e);
        }
    }

    /**
     * 解析图片内容
     */
    private String parseImageText(ActivityMessage message) {
        try {
            List<String> fullHttpsUrls = getFullHttpsUrls(message);
            String ea = gdsHandler.getEAByEI(message.getTenantId());
            return getAiComplete(new User(message.getTenantId(), message.getOpId()),"prompt_sfa_image_ocr", fullHttpsUrls,message.getObjectId());
        } catch (Exception e) {
            log.error("Error parsing image text for message: {}", message, e);
            return "";
        }
    }

    public String getAiComplete(User user,String apiName, List<String> imageStrings, String dataId){
        PromptCompletions.Arg arg = new PromptCompletions.Arg();
        PromptCompletions.ImageSetting imageSetting = new PromptCompletions.ImageSetting();
        imageSetting.setSceneVariableName("imageURL");
        arg.setImageStrings(imageStrings);
        arg.setApiName(apiName);
        arg.setImageSetting(imageSetting);
        arg.setSupportImage(Boolean.TRUE);
        Map<String, String> sceneParamMap = new HashMap<>();
        if(ObjectUtils.isNotEmpty(sceneParamMap)){
            arg.setSceneVariables(sceneParamMap);
        }
        if(ObjectUtils.isNotEmpty(dataId)){
            arg.setBingObjectDataId(dataId);
        }
        String aiResult = requestCompletion(user, arg);
        if(ObjectUtils.isEmpty(aiResult)){
            log.error("ActivitySummaryService aiRestProxy.completions resposne is null");
            return "";
        }
        return ActivityUtils.replaceStr(aiResult);
    }


    public String requestCompletion(User user, PromptCompletions.Arg arg) {
        arg.setSupportAdvanced(true);
        if (ObjectUtils.isEmpty(user.getUserId()) || "-10000".equals(user.getUserId())){
            log.warn("requestCompletion userId is system");
        }
        AiRestProxyModel.Resposne completions = new AiRestProxyModel.Resposne();
        try {
            completions = aiRestProxy.completions(arg, AiRestProxy.getHeaders(user.getTenantId(), user.getUserId()));
        }catch (Exception e){
            log.error("requestCompletion exception e: ",e);
        }
        if (completions != null && completions.getErrCode() != 0) {
            log.error("requestCompletion error, code:{}, message:{}", completions.getErrCode(), completions.getErrMessage());
            return "";
        }
        return Optional.ofNullable(completions)
                .map(AiRestProxyModel.Resposne::getResult)
                .map(AiRestProxyModel.Result::getMessage)
                .orElse("");
    }

}
