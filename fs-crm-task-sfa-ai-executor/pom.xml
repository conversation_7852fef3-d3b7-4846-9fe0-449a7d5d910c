<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fs-crm-task-sfa-ai</artifactId>
        <groupId>com.facishare</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>fs-crm-task-sfa-ai-executor</artifactId>
    <name>fs-crm-task-sfa-ai-executor</name>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-eservice-rest-api</artifactId>
            <version>9.1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>io.grpc</groupId>
                    <artifactId>grpc-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-change-set-api</artifactId>
            <version>2.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-task-sfa-ai-common</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-task-sfa-correct</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-sfa-lto</artifactId>
            <version>9.5.5-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>fs-metadata-provider</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-qixin-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-qixin-api</artifactId>
            <version>0.1.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-fsi-proxy</artifactId>
            <version>${fs-fsi-proxy.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>dispatcher-support</artifactId>
            <version>2.2.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.msg</groupId>
            <artifactId>fs-message-api</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-sfa-audit-log</artifactId>
            <version>1.1.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-paas-ai-api</artifactId>
            <version>1.0.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>4.7.0</version>
        </dependency>
    </dependencies>
</project>
