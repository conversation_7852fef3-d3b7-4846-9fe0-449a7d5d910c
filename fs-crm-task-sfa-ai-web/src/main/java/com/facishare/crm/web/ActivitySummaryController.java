package com.facishare.crm.web;

import com.alibaba.fastjson2.JSON;
import com.facishare.crm.sfa.lto.activity.mongo.ParagraphMongoDao;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.activitysummary.model.ActivityTagMessage;
import com.facishare.crm.task.sfa.activitysummary.model.AttendeesInsightModel;
import com.facishare.crm.task.sfa.activitysummary.service.*;
import com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.AttendeesInsightService;
import com.facishare.crm.task.sfa.activitysummary.service.paragraph.ActivityParagraphService;
import com.facishare.crm.task.sfa.activitysummary.service.tag.ActivityTagService;
import com.facishare.crm.task.sfa.activitysummary.service.strategy.DirectTaggingService;
import com.facishare.crm.task.sfa.rest.dto.EgressApiModels;
import com.facishare.crm.task.sfa.xxl.ActivitySummaryJobHandler;
import com.facishare.crm.task.sfa.xxl.StrategyTaskJobHandler;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR> gongchunru
 * @date : 2023/12/20 11:17
 * @description:
 */
@Slf4j
@RestController
@RequestMapping("activity_summary")
public class ActivitySummaryController {
    @Autowired
    private ActivitySummaryService activitySummaryService;

    @Autowired
    private ActivitySummaryJobHandler activitySummaryJobHandler;
    
    @Autowired
    private StrategyTaskJobHandler strategyTaskJobHandler;
    @Autowired
    private EmotionAnalysisService emotionAnalysisService;

    @Resource
    Rec2TextService rec2TextService;

    @Autowired
    AccountStrategyService strategyService;

    @Autowired
    ActivityMeetingSummaryServiceImpl activityMeetingSummaryService;
    @Autowired
    ActivityParagraphService activityParagraphService;

    @Autowired
    AttendeesInsightService attendeesInsightService;

    @Autowired
    private ActivityTagService activityTagService;

    @Autowired
    private DirectTaggingService directTaggingService;

    @Autowired
    AttachmentStrategy attachmentStrategy;

    @Autowired
    AudioAttachment audioAttachment;

    @GetMapping("execute")
    public Object activitySummary() {
        activitySummaryJobHandler.exexute();
        return "hello world";
    }

    @PostMapping("processSummary")
    public Object activitySummary(@RequestParam("tenantId") String tenantId,
                                  @RequestParam("objectId") String objectId,
                                  @RequestParam("sessionId") String sessionId) {
        activitySummaryService.processAllSummary(tenantId, objectId, sessionId);
        return "success";
    }


    /**
     * @return
     */
    @PostMapping("rec2text")
    public Object rec2text(@RequestBody ActivityMessage message) {
        //ActivityMessage message = ActivityMessage.builder().tenantId(tenantId).objectId(objectId).objectApiName(objectApiName).op(op).build();
        rec2TextService.rec2Text(message);
        return "success";
    }

    /**
     * @return
     */
    @PostMapping("accountStrategy")
    public Object accountStrategy(@RequestBody ActivityMessage message) {
        strategyService.consume(message);
        return "success";
    }

    /**
     * @return
     */
    @PostMapping("activityParagraph")
    public Object activityParagraphService(@RequestBody ActivityMessage message) {
        String tenantId = message.getTenantId();
        String objectId = message.getObjectId();
        activityParagraphService.processDocumentsWithSegmentation(User.systemUser(tenantId), objectId);
        return "success";
    }


    @Resource
    private ParagraphMongoDao paragraphMongoDao;

    /**
     * @return
     */
    @PostMapping("queryParagraphTest")
    public Object queryParagraphTest(@RequestBody ActivityMessage message) {
        /*List<ParagraphDocument> tags = paragraphMongoDao.queryByIdsWithFields(message.getTenantId(), "6810c20c0aa466437508e93c", Lists.newArrayList("tags"));
        tags.forEach(tag -> {
            log.info("tags:{}", tag);
        });*/
        return "success";
    }

    /**
     * @return
     */
    @PostMapping("consumerFileToText")
    public Object consumerFileToText(@RequestBody ActivityMessage message) {
        activityParagraphService.processTextWithSegmentation( User.systemUser(message.getTenantId()),message.getObjectId());
        return "success";
    }


    @PostMapping("emotionAnalysis")
    public Object emotionAnalysis(@RequestBody ActivityMessage message) {
        emotionAnalysisService.generate(message);
        return "success";
    }

    @PostMapping("meetingSummary")
    public Object meetingSummary(@RequestBody ActivityMessage message) {
        activityMeetingSummaryService.consume(message);
        return "success";
    }

    @PostMapping("componentSummary")
    public Object componentSummary(@RequestBody Map<String, String> body) {
        ActivityMessage message = ActivityMessage.builder()
                .tenantId(body.get("tenantId"))
                .objectId(body.get("objectId"))
                .opId(body.get("opId"))
                .language(body.get("language"))
                .build();
        String result = activityMeetingSummaryService.AskForAIv2(message, body.get("apiName"), "");
        return result;
    }

    @PostMapping("attachment")
    public String attachment(@RequestBody ActivityMessage message) {
        attachmentStrategy.handleFile(message);
        return "success";
    }


    @PostMapping("getAsrRecTaskResult")
    public String attachment2(@RequestBody Map<String, String> taskIdMap) {
        EgressApiModels.AsrRecTask.TaskResult rst = rec2TextService.getAsrRecTaskResult(taskIdMap.get("taskId"));
        return JSON.toJSONString(rst);
    }

    @PostMapping("createAsrRecTask")
    public String createAsrRecTask(@RequestBody Map<String, String> paramMap) {
        String taskId = rec2TextService.createAsrRecTask(paramMap.get("url"), 1,
                1, 0);
        return taskId;
    }

    @PostMapping("attendeesInsight")
    public String attendeesInsight(@RequestBody AttendeesInsightModel.AttendeesInsightMessage message) {
        attendeesInsightService.insight(message);
        return "success";
    }


    @PostMapping("getNoSignAcUrl")
    public String getNoSignAcUrl(@RequestBody Map<String, String> paramMap) {
        return audioAttachment.getNoSignAcUrl(paramMap.get("ei"), paramMap.get("ea"), "1000", paramMap.get("path"));
    }


    /**
     * 处理活动标签
     *
     * @param activityTagMessage 活动标签消息
     * @return 处理结果
     */
    @PostMapping("activity_tag")
    public Object processActivityTag(@RequestBody ActivityTagMessage activityTagMessage) {
        log.info("Received activity tag request: {}", activityTagMessage);
        activityTagService.consumer(activityTagMessage);
        return "success";
    }

    /**
     * 测试 DirectTaggingService
     *
     * @param tenantId 租户ID
     * @param objectId 对象ID
     * @param type     类型
     * @return a
     */
    @PostMapping("directTaggingTest")
    public Object directTaggingTest(@RequestParam("tenantId") String tenantId,
                                    @RequestParam("objectId") String objectId,
                                    @RequestParam("type") String type) {
        log.info("Received direct tagging test request, tenantId: {}, objectId: {}, type: {}", tenantId, objectId, type);
        ActivityMessage activityMessage = ActivityMessage.builder()
                .tenantId(tenantId)
                .objectId(objectId)
                .build();
        directTaggingService.process(User.systemUser(tenantId), activityMessage, type);
        return "success";
    }

    /**
     * 根据任务ID执行策略任务
     *
     * @param taskId 任务ID
     * @return 执行结果
     */
    @PostMapping("executeStrategyTask")
    public Object executeStrategyTask(@RequestParam("taskId") String taskId) {
        log.info("Received execute strategy task request, taskId: {}", taskId);
        
        try {
            boolean result = strategyTaskJobHandler.executeTaskById(taskId);
            if (result) {
                return "Task executed successfully";
            } else {
                return "Task execution failed";
            }
        } catch (Exception e) {
            log.error("Error executing strategy task: {}", taskId, e);
            return "Task execution error: " + e.getMessage();
        }
    }
}
