package com.facishare.crm.web;

import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.activitysummary.service.ActivityMeetingSummaryServiceImpl;
import com.facishare.crm.task.sfa.activitysummary.service.AttachmentRebuildService;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.fxiaoke.functions.utils.Maps;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 组件生成补偿接口
 */
@Slf4j
@RestController
@RequestMapping("/activity_rebuild")
public class ActivityRebuildService {

    @Resource
    private ActivityMeetingSummaryServiceImpl activityMeetingSummaryService;
    
    @Resource
    private AttachmentRebuildService attachmentRebuildService;

    /**
     * 会议摘要生成
     */
    @PostMapping("/meeting_summary")
    public Map<String, String> meetingSummary(@RequestBody Map<String, String> arg) {
        if (ObjectUtils.isEmpty(arg.get("objectId")) || ObjectUtils.isEmpty(arg.get("tenantId"))) {
            return Maps.of("errMsg", "objectId or tenantId is null");
        }
        String traceId = UUID.randomUUID().toString().replace("-", "");
        TraceContext.get().setTraceId(traceId);
        log.info("meetingSummary. arg={}", arg);
        ActivityMessage message = ActivityMessage.builder()
                .tenantId(arg.get("tenantId"))
                .objectId(arg.get("objectId"))
                .objectApiName("ActiveRecordObj")
                .language(arg.getOrDefault("language", "zh_cn"))
                .build();
        ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
        if (ObjectUtils.isEmpty(arg.get("apiName"))) {
            task.submit(() -> activityMeetingSummaryService.consume(message)).run();
        } else {
            task.submit(() -> activityMeetingSummaryService.executeByApiName(message, arg.get("apiName"))).run();
        }
        return Maps.of("traceId", traceId);
    }

    /**
     * 历史附件解析处理
     * 用于对历史ActiveRecordObj中的附件进行批量解析处理
     */
    @PostMapping("/attachment_parse")
    public Map<String, Object> attachmentParse(@RequestBody Map<String, Object> arg) {
        List<String> tenantIds = (List<String>) arg.get("tenant_id");
        
        if (ObjectUtils.isEmpty(tenantIds)) {
            return Maps.of("errMsg", "tenant_id is null");
        }
        
        if (tenantIds.isEmpty()) {
            return Maps.of("errMsg", "tenant_id list is empty");
        }
        
        String traceId = UUID.randomUUID().toString().replace("-", "");
        TraceContext.get().setTraceId(traceId);
        log.info("attachmentParse start. tenantIds={}, arg={}", tenantIds, arg);
        
        try {
            // 可选参数
            String startDate = (String) arg.get("startDate"); // 开始日期 yyyy-MM-dd
            String endDate = (String) arg.get("endDate");     // 结束日期 yyyy-MM-dd
            
            int totalProcessedCount = attachmentRebuildService.processHistoricalAttachments(
                tenantIds, startDate, endDate
            );
            
            return Maps.of(
                "traceId", traceId,
                "processedCount", totalProcessedCount,
                "tenantCount", tenantIds.size(),
                "status", "success"
            );
        } catch (Exception e) {
            log.error("attachmentParse error", e);
            return Maps.of(
                "traceId", traceId,
                "errMsg", e.getMessage(),
                "status", "failed"
            );
        }
    }

}
