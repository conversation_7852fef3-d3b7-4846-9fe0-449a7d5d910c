[{"id": "686e2e6df847ab0001abf02e", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "67ed12c8829bce000139a8d5", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed11e8829bce0001398b61", "must_do": false, "task_order": 10, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752051310017, "last_modified_by": "1224", "last_modified_time": 1752051310185, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462571165}, {"id": "686e2e8ff847ab0001abfe16", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "686e2a18f847ab0001a9995b", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed11e8829bce0001398b61", "must_do": false, "task_order": 20, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752051343199, "last_modified_by": "1224", "last_modified_time": 1752051343357, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462571261}, {"id": "686e2e9ef847ab0001ac09fe", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "686e2a38f847ab0001a9a51c", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed11e8829bce0001398b61", "must_do": false, "task_order": 30, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752051358477, "last_modified_by": "1224", "last_modified_time": 1752051358562, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462571347}, {"id": "686e2ef2f847ab0001ac2838", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "6836b28a938aa30001c8bead", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed1206829bce0001398f4f", "must_do": false, "task_order": 10, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752051442513, "last_modified_by": "1224", "last_modified_time": 1752051442643, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462569708}, {"id": "686e2f20f847ab0001ac330f", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "686e2a73f847ab0001a9bd95", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed1206829bce0001398f4f", "must_do": false, "task_order": 20, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752051488384, "last_modified_by": "1224", "last_modified_time": 1752051488557, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462569956}, {"id": "686e2f2ff847ab0001ac3881", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "67ed1348829bce000139aec8", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed1206829bce0001398f4f", "must_do": false, "task_order": 30, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752051504091, "last_modified_by": "1224", "last_modified_time": 1752051504201, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462570092}, {"id": "686e2f4bf847ab0001ac4027", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "67ed1376829bce000139b7a9", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed1206829bce0001398f4f", "must_do": false, "task_order": 40, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752051531373, "last_modified_by": "1224", "last_modified_time": 1752051531504, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462570182}, {"id": "686e2f5ef847ab0001ac465e", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "686e2a18f847ab0001a9995b", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed1206829bce0001398f4f", "must_do": false, "task_order": 50, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752051551136, "last_modified_by": "1224", "last_modified_time": 1752051551251, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462570264}, {"id": "686e2f6ff847ab0001ac503b", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "686e2a38f847ab0001a9a51c", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed1206829bce0001398f4f", "must_do": false, "task_order": 60, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752051567555, "last_modified_by": "1224", "last_modified_time": 1752051567656, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462570343}, {"id": "686e2fb4f847ab0001ac8fd9", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "6836b368938aa30001c8cf05", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836ad89a7c3f00001710a75", "must_do": false, "task_order": 10, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752051637231, "last_modified_by": "1224", "last_modified_time": 1752051637481, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462570430}, {"id": "686e2fc5f847ab0001ac9ba2", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "686e2a88f847ab0001a9c75b", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836ad89a7c3f00001710a75", "must_do": false, "task_order": 20, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752051653975, "last_modified_by": "1224", "last_modified_time": 1752051654144, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462570499}, {"id": "686e2fd2f847ab0001ac9f9a", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "686e2a9af847ab0001a9cc2d", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836ad89a7c3f00001710a75", "must_do": false, "task_order": 30, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752051666721, "last_modified_by": "1224", "last_modified_time": 1752051666887, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462570590}, {"id": "686e2fe0f847ab0001aca537", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "686e2a51f847ab0001a9b63b", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836ad89a7c3f00001710a75", "must_do": false, "task_order": 40, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752051680296, "last_modified_by": "1224", "last_modified_time": 1752051680411, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462570667}, {"id": "686e2fedf847ab0001aca9bf", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "686e2a18f847ab0001a9995b", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836ad89a7c3f00001710a75", "must_do": false, "task_order": 50, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752051693609, "last_modified_by": "1224", "last_modified_time": 1752051693900, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462570730}, {"id": "686e2ffcf847ab0001acaec3", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "686e2a38f847ab0001a9a51c", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836ad89a7c3f00001710a75", "must_do": false, "task_order": 60, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752051709049, "last_modified_by": "1224", "last_modified_time": 1752051709169, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462570805}, {"id": "686e30f8f847ab0001acf57b", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "680f714b1607ea000175adb6", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed1262829bce0001399d1b", "must_do": false, "task_order": 10, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752051960232, "last_modified_by": "1224", "last_modified_time": 1752051960354, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462571416}, {"id": "686e3106f847ab0001ad020e", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "680f71b51607ea000175b87e", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed1262829bce0001399d1b", "must_do": false, "task_order": 20, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752051974616, "last_modified_by": "1224", "last_modified_time": 1752051974765, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462571480}, {"id": "686e31b0f847ab0001ad2e57", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "686e2d7cf847ab0001ab8442", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed1262829bce0001399d1b", "must_do": false, "task_order": 50, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752052144907, "last_modified_by": "1224", "last_modified_time": 1752052145111, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462571579}, {"id": "686e31bef847ab0001ad36e9", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "686e2d99f847ab0001abaf3f", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed1262829bce0001399d1b", "must_do": false, "task_order": 60, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752052158264, "last_modified_by": "1224", "last_modified_time": 1752052158447, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462571655}, {"id": "686e31cbf847ab0001ad3b07", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "686e2d8df847ab0001aba83a", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed1262829bce0001399d1b", "must_do": false, "task_order": 70, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752052171624, "last_modified_by": "1224", "last_modified_time": 1752052171825, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462571730}, {"id": "686e31ecf847ab0001ad502f", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "680f71f01607ea000175c018", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed124b829bce00013998b3", "must_do": false, "task_order": 10, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752052205106, "last_modified_by": "1224", "last_modified_time": 1752052205418, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462571797}, {"id": "686e31f9f847ab0001ad58e7", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "6836b3e9938aa30001c8dda8", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed124b829bce00013998b3", "must_do": false, "task_order": 20, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752052217751, "last_modified_by": "1224", "last_modified_time": 1752052217900, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462571858}, {"id": "686e3206f847ab0001ad5e7f", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "686e2d7cf847ab0001ab8442", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed124b829bce00013998b3", "must_do": false, "task_order": 30, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752052231022, "last_modified_by": "1224", "last_modified_time": 1752052231271, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462571933}, {"id": "686e3212f847ab0001ad62ca", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "686e2d7cf847ab0001ab8442", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed124b829bce00013998b3", "must_do": false, "task_order": 40, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752052243109, "last_modified_by": "1224", "last_modified_time": 1752052243225, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462571997}, {"id": "686e3222f847ab0001ad6ede", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "686e2d8df847ab0001aba83a", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed124b829bce00013998b3", "must_do": false, "task_order": 50, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752052258527, "last_modified_by": "1224", "last_modified_time": 1752052258822, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462572067}, {"id": "686e323af847ab0001ad74ab", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "6836b404938aa30001c8e011", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836aed1938aa30001c8898e", "must_do": false, "task_order": 10, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752052282944, "last_modified_by": "1224", "last_modified_time": 1752052283075, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462572148}, {"id": "686e3247f847ab0001ad7b09", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "686e2d7cf847ab0001ab8442", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836aed1938aa30001c8898e", "must_do": false, "task_order": 20, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752052295984, "last_modified_by": "1224", "last_modified_time": 1752052296096, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462572211}, {"id": "686e3252f847ab0001ad7f90", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "686e2d99f847ab0001abaf3f", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836aed1938aa30001c8898e", "must_do": false, "task_order": 30, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752052306668, "last_modified_by": "1224", "last_modified_time": 1752052306745, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462572270}, {"id": "686e325df847ab0001ad8590", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "686e2d8df847ab0001aba83a", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836aed1938aa30001c8898e", "must_do": false, "task_order": 40, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752052317689, "last_modified_by": "1224", "last_modified_time": 1752052317816, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462572345}, {"id": "686e326ff847ab0001ad9088", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "6836b419938aa30001c8e218", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836af01938aa30001c88da4", "must_do": false, "task_order": 10, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752052335375, "last_modified_by": "1224", "last_modified_time": 1752052335473, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462572407}, {"id": "686e327bf847ab0001ad9630", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "6836b42b938aa30001c8e40e", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836af01938aa30001c88da4", "must_do": false, "task_order": 20, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752052347750, "last_modified_by": "1224", "last_modified_time": 1752052347842, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462572577}, {"id": "686e3285f847ab0001ad9a5f", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "6836b43e938aa30001c8e677", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836af01938aa30001c88da4", "must_do": false, "task_order": 30, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752052357994, "last_modified_by": "1224", "last_modified_time": 1752052358105, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462572655}, {"id": "686e3290f847ab0001ada148", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "686e2d7cf847ab0001ab8442", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836af01938aa30001c88da4", "must_do": false, "task_order": 40, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752052368415, "last_modified_by": "1224", "last_modified_time": 1752052368521, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462572716}, {"id": "686e329df847ab0001adab97", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "686e2d99f847ab0001abaf3f", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836af01938aa30001c88da4", "must_do": false, "task_order": 50, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752052381527, "last_modified_by": "1224", "last_modified_time": 1752052381797, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462572791}, {"id": "686e32bff847ab0001adc670", "tenant_id": "71653", "name": "L2C管理流程任务", "task_id": "686e2d8df847ab0001aba83a", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836af01938aa30001c88da4", "must_do": false, "task_order": 60, "step_by_step": false, "system_type": null, "display_name": null, "owner": "1224", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "1224", "create_time": 1752052415403, "last_modified_by": "1224", "last_modified_time": 1752052415520, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "1000", "data_own_organization": null, "data_auth_code": "21320324fc6b8b99", "change_type": null, "out_data_auth_code": "0f4911d65c6a482c", "order_by": null, "data_auth_id": 7799990, "out_data_auth_id": 630971, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752052462572854}]